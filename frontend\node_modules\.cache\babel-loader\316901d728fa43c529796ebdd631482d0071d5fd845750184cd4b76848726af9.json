{"ast": null, "code": "/**\n * Team Logo Utilities\n * Centralized system for handling team logo URLs consistently across the application\n */\nimport React from 'react';\n\n// Get the correct base URL for team logos\nconst getLogoBaseUrl = () => {\n  // Check if we're in development mode\n  const isDevelopment = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';\n  if (isDevelopment) {\n    // In development, use the proxy path\n    return '/FanBet247/backend';\n  } else {\n    // In production, construct the full URL\n    const projectPath = window.location.pathname.split('/')[1] || 'FanBet247';\n    return `${window.location.origin}/${projectPath}/backend`;\n  }\n};\n\n/**\n * Get the complete URL for a team logo\n * @param {string|Object} teamData - Either team name string or team object with logo property\n * @param {Array} teams - Array of team objects (optional, used when teamData is a string)\n * @returns {string} Complete logo URL or default logo URL\n */\nexport const getTeamLogoUrl = (teamData, teams = []) => {\n  let logoPath = null;\n\n  // Handle different input types\n  if (typeof teamData === 'string') {\n    // teamData is a team name, find the team in the teams array\n    const team = teams.find(t => t.name === teamData);\n    logoPath = team === null || team === void 0 ? void 0 : team.logo;\n  } else if (teamData && typeof teamData === 'object') {\n    // teamData is a team object\n    logoPath = teamData.logo;\n  }\n\n  // If no logo path found, return default\n  if (!logoPath) {\n    return '/images/default-team-logo.png';\n  }\n\n  // Construct the complete URL\n  const baseUrl = getLogoBaseUrl();\n\n  // Handle different logo path formats\n  if (logoPath.startsWith('http')) {\n    // Already a complete URL\n    return logoPath;\n  } else if (logoPath.startsWith('/')) {\n    // Absolute path from root\n    return `${baseUrl}${logoPath}`;\n  } else {\n    // Relative path (most common case: \"uploads/filename.png\")\n    return `${baseUrl}/${logoPath}`;\n  }\n};\n\n/**\n * Get team logo URL by team name\n * @param {string} teamName - Name of the team\n * @param {Array} teams - Array of team objects\n * @returns {string} Complete logo URL or default logo URL\n */\nexport const getTeamLogoByName = (teamName, teams) => {\n  return getTeamLogoUrl(teamName, teams);\n};\n\n/**\n * Get team logo URL from team object\n * @param {Object} team - Team object with logo property\n * @returns {string} Complete logo URL or default logo URL\n */\nexport const getTeamLogoFromObject = team => {\n  return getTeamLogoUrl(team);\n};\n\n/**\n * Create a team logo component with error handling\n * @param {string} teamName - Name of the team\n * @param {Array} teams - Array of team objects\n * @param {Object} props - Additional props for the img element\n * @returns {JSX.Element} Image element with error handling\n */\nexport const createTeamLogoElement = (teamName, teams, props = {}) => {\n  const logoUrl = getTeamLogoByName(teamName, teams);\n  return /*#__PURE__*/React.createElement('img', {\n    src: logoUrl,\n    alt: teamName || 'Team Logo',\n    onError: e => {\n      e.target.src = '/images/default-team-logo.png';\n    },\n    ...props\n  });\n};\n\n/**\n * Hook for team logo functionality\n * @param {Array} teams - Array of team objects\n * @returns {Object} Object with logo utility functions\n */\nexport const useTeamLogos = teams => {\n  const getLogoUrl = teamName => getTeamLogoByName(teamName, teams);\n  const getLogoFromObject = team => getTeamLogoFromObject(team);\n  const createLogoElement = (teamName, props = {}) => createTeamLogoElement(teamName, teams, props);\n  return {\n    getLogoUrl,\n    getLogoFromObject,\n    createLogoElement,\n    teams\n  };\n};\n\n// Default export for backward compatibility\nexport default {\n  getTeamLogoUrl,\n  getTeamLogoByName,\n  getTeamLogoFromObject,\n  createTeamLogoElement,\n  useTeamLogos\n};", "map": {"version": 3, "names": ["React", "getLogoBaseUrl", "isDevelopment", "process", "env", "NODE_ENV", "window", "location", "hostname", "projectPath", "pathname", "split", "origin", "getTeamLogoUrl", "teamData", "teams", "logoPath", "team", "find", "t", "name", "logo", "baseUrl", "startsWith", "getTeamLogoByName", "teamName", "getTeamLogoFromObject", "createTeamLogoElement", "props", "logoUrl", "createElement", "src", "alt", "onError", "e", "target", "useTeamLogos", "getLogoUrl", "getLogoFromObject", "createLogoElement"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/utils/teamLogoUtils.js"], "sourcesContent": ["/**\n * Team Logo Utilities\n * Centralized system for handling team logo URLs consistently across the application\n */\nimport React from 'react';\n\n// Get the correct base URL for team logos\nconst getLogoBaseUrl = () => {\n    // Check if we're in development mode\n    const isDevelopment = process.env.NODE_ENV === 'development' ||\n                         window.location.hostname === 'localhost' ||\n                         window.location.hostname === '127.0.0.1';\n\n    if (isDevelopment) {\n        // In development, use the proxy path\n        return '/FanBet247/backend';\n    } else {\n        // In production, construct the full URL\n        const projectPath = window.location.pathname.split('/')[1] || 'FanBet247';\n        return `${window.location.origin}/${projectPath}/backend`;\n    }\n};\n\n/**\n * Get the complete URL for a team logo\n * @param {string|Object} teamData - Either team name string or team object with logo property\n * @param {Array} teams - Array of team objects (optional, used when teamData is a string)\n * @returns {string} Complete logo URL or default logo URL\n */\nexport const getTeamLogoUrl = (teamData, teams = []) => {\n    let logoPath = null;\n\n    // Handle different input types\n    if (typeof teamData === 'string') {\n        // teamData is a team name, find the team in the teams array\n        const team = teams.find(t => t.name === teamData);\n        logoPath = team?.logo;\n    } else if (teamData && typeof teamData === 'object') {\n        // teamData is a team object\n        logoPath = teamData.logo;\n    }\n\n    // If no logo path found, return default\n    if (!logoPath) {\n        return '/images/default-team-logo.png';\n    }\n\n    // Construct the complete URL\n    const baseUrl = getLogoBaseUrl();\n    \n    // Handle different logo path formats\n    if (logoPath.startsWith('http')) {\n        // Already a complete URL\n        return logoPath;\n    } else if (logoPath.startsWith('/')) {\n        // Absolute path from root\n        return `${baseUrl}${logoPath}`;\n    } else {\n        // Relative path (most common case: \"uploads/filename.png\")\n        return `${baseUrl}/${logoPath}`;\n    }\n};\n\n/**\n * Get team logo URL by team name\n * @param {string} teamName - Name of the team\n * @param {Array} teams - Array of team objects\n * @returns {string} Complete logo URL or default logo URL\n */\nexport const getTeamLogoByName = (teamName, teams) => {\n    return getTeamLogoUrl(teamName, teams);\n};\n\n/**\n * Get team logo URL from team object\n * @param {Object} team - Team object with logo property\n * @returns {string} Complete logo URL or default logo URL\n */\nexport const getTeamLogoFromObject = (team) => {\n    return getTeamLogoUrl(team);\n};\n\n/**\n * Create a team logo component with error handling\n * @param {string} teamName - Name of the team\n * @param {Array} teams - Array of team objects\n * @param {Object} props - Additional props for the img element\n * @returns {JSX.Element} Image element with error handling\n */\nexport const createTeamLogoElement = (teamName, teams, props = {}) => {\n    const logoUrl = getTeamLogoByName(teamName, teams);\n    \n    return React.createElement('img', {\n        src: logoUrl,\n        alt: teamName || 'Team Logo',\n        onError: (e) => {\n            e.target.src = '/images/default-team-logo.png';\n        },\n        ...props\n    });\n};\n\n/**\n * Hook for team logo functionality\n * @param {Array} teams - Array of team objects\n * @returns {Object} Object with logo utility functions\n */\nexport const useTeamLogos = (teams) => {\n    const getLogoUrl = (teamName) => getTeamLogoByName(teamName, teams);\n    \n    const getLogoFromObject = (team) => getTeamLogoFromObject(team);\n    \n    const createLogoElement = (teamName, props = {}) => \n        createTeamLogoElement(teamName, teams, props);\n\n    return {\n        getLogoUrl,\n        getLogoFromObject,\n        createLogoElement,\n        teams\n    };\n};\n\n// Default export for backward compatibility\nexport default {\n    getTeamLogoUrl,\n    getTeamLogoByName,\n    getTeamLogoFromObject,\n    createTeamLogoElement,\n    useTeamLogos\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;EACzB;EACA,MAAMC,aAAa,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IACvCC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,WAAW,IACxCF,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,WAAW;EAE7D,IAAIN,aAAa,EAAE;IACf;IACA,OAAO,oBAAoB;EAC/B,CAAC,MAAM;IACH;IACA,MAAMO,WAAW,GAAGH,MAAM,CAACC,QAAQ,CAACG,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW;IACzE,OAAO,GAAGL,MAAM,CAACC,QAAQ,CAACK,MAAM,IAAIH,WAAW,UAAU;EAC7D;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,cAAc,GAAGA,CAACC,QAAQ,EAAEC,KAAK,GAAG,EAAE,KAAK;EACpD,IAAIC,QAAQ,GAAG,IAAI;;EAEnB;EACA,IAAI,OAAOF,QAAQ,KAAK,QAAQ,EAAE;IAC9B;IACA,MAAMG,IAAI,GAAGF,KAAK,CAACG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKN,QAAQ,CAAC;IACjDE,QAAQ,GAAGC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,IAAI;EACzB,CAAC,MAAM,IAAIP,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IACjD;IACAE,QAAQ,GAAGF,QAAQ,CAACO,IAAI;EAC5B;;EAEA;EACA,IAAI,CAACL,QAAQ,EAAE;IACX,OAAO,+BAA+B;EAC1C;;EAEA;EACA,MAAMM,OAAO,GAAGrB,cAAc,CAAC,CAAC;;EAEhC;EACA,IAAIe,QAAQ,CAACO,UAAU,CAAC,MAAM,CAAC,EAAE;IAC7B;IACA,OAAOP,QAAQ;EACnB,CAAC,MAAM,IAAIA,QAAQ,CAACO,UAAU,CAAC,GAAG,CAAC,EAAE;IACjC;IACA,OAAO,GAAGD,OAAO,GAAGN,QAAQ,EAAE;EAClC,CAAC,MAAM;IACH;IACA,OAAO,GAAGM,OAAO,IAAIN,QAAQ,EAAE;EACnC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMQ,iBAAiB,GAAGA,CAACC,QAAQ,EAAEV,KAAK,KAAK;EAClD,OAAOF,cAAc,CAACY,QAAQ,EAAEV,KAAK,CAAC;AAC1C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMW,qBAAqB,GAAIT,IAAI,IAAK;EAC3C,OAAOJ,cAAc,CAACI,IAAI,CAAC;AAC/B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMU,qBAAqB,GAAGA,CAACF,QAAQ,EAAEV,KAAK,EAAEa,KAAK,GAAG,CAAC,CAAC,KAAK;EAClE,MAAMC,OAAO,GAAGL,iBAAiB,CAACC,QAAQ,EAAEV,KAAK,CAAC;EAElD,oBAAOf,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IAC9BC,GAAG,EAAEF,OAAO;IACZG,GAAG,EAAEP,QAAQ,IAAI,WAAW;IAC5BQ,OAAO,EAAGC,CAAC,IAAK;MACZA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG,+BAA+B;IAClD,CAAC;IACD,GAAGH;EACP,CAAC,CAAC;AACN,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMQ,YAAY,GAAIrB,KAAK,IAAK;EACnC,MAAMsB,UAAU,GAAIZ,QAAQ,IAAKD,iBAAiB,CAACC,QAAQ,EAAEV,KAAK,CAAC;EAEnE,MAAMuB,iBAAiB,GAAIrB,IAAI,IAAKS,qBAAqB,CAACT,IAAI,CAAC;EAE/D,MAAMsB,iBAAiB,GAAGA,CAACd,QAAQ,EAAEG,KAAK,GAAG,CAAC,CAAC,KAC3CD,qBAAqB,CAACF,QAAQ,EAAEV,KAAK,EAAEa,KAAK,CAAC;EAEjD,OAAO;IACHS,UAAU;IACVC,iBAAiB;IACjBC,iBAAiB;IACjBxB;EACJ,CAAC;AACL,CAAC;;AAED;AACA,eAAe;EACXF,cAAc;EACdW,iBAAiB;EACjBE,qBAAqB;EACrBC,qBAAqB;EACrBS;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}