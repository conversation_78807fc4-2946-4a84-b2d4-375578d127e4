<?php
require_once 'backend/includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    echo "Checking OTP records:\n";
    $stmt = $conn->prepare("SELECT * FROM user_otp WHERE user_id = 1 ORDER BY created_at DESC LIMIT 3");
    $stmt->execute();
    $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($records as $record) {
        echo "ID: {$record['id']}\n";
        echo "User ID: {$record['user_id']}\n";
        echo "OTP: {$record['otp']}\n";
        echo "Expiry: {$record['expiry']}\n";
        echo "Used: {$record['used']}\n";
        echo "Attempts: {$record['attempts']}\n";
        echo "Created: {$record['created_at']}\n";
        echo "Current time: " . date('Y-m-d H:i:s') . "\n";
        echo "Is expired? " . ($record['expiry'] < date('Y-m-d H:i:s') ? 'Yes' : 'No') . "\n";
        echo "---\n";
    }
    
    echo "\nTesting query:\n";
    $stmt = $conn->prepare("
        SELECT id, otp, expiry, attempts, used 
        FROM user_otp 
        WHERE user_id = ? AND used = 0 AND expiry > NOW()
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute([1]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "Found valid OTP: {$result['otp']}\n";
    } else {
        echo "No valid OTP found\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
