<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db_connect.php';
require_once '../vendor/autoload.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

// Function to send OTP email
function sendUserOtp($userId, $email, $conn) {
    try {
        // Generate 6-digit OTP
        $otp = sprintf('%06d', mt_rand(0, 999999));

        // Set expiry time (5 minutes from now)
        $expiresAt = date('Y-m-d H:i:s', time() + 300);

        // Store OTP in database (delete old unused OTPs first)
        $stmt = $conn->prepare("DELETE FROM user_otp WHERE user_id = ? AND used = 0");
        $stmt->execute([$userId]);

        $stmt = $conn->prepare("
            INSERT INTO user_otp (user_id, otp, expiry)
            VALUES (?, ?, ?)
        ");
        $stmt->execute([$userId, $otp, $expiresAt]);

        // Get SMTP settings
        $stmt = $conn->prepare("SELECT * FROM smtp_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1");
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            return false; // No SMTP configured
        }

        $smtp = $stmt->fetch(PDO::FETCH_ASSOC);

        // Set up PHPMailer
        $mail = new PHPMailer(true);

        // Server settings
        $mail->isSMTP();
        $mail->Host = $smtp['host'];
        $mail->SMTPAuth = true;
        $mail->Username = $smtp['username'];
        $mail->Password = $smtp['password'];
        $mail->SMTPSecure = $smtp['encryption'] === 'ssl' ? PHPMailer::ENCRYPTION_SMTPS : PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = $smtp['port'];

        // Recipients
        $mail->setFrom($smtp['from_email'], $smtp['from_name']);
        $mail->addAddress($email);

        // Content
        $mail->isHTML(true);
        $mail->Subject = 'FanBet247 - Login Verification Code';
        $mail->Body = "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #52B788; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background-color: #f9f9f9; }
                .otp-code { font-size: 24px; font-weight: bold; color: #52B788; text-align: center; padding: 20px; background-color: white; border: 2px solid #52B788; border-radius: 8px; margin: 20px 0; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>FanBet247</h1>
                    <h2>Login Verification</h2>
                </div>
                <div class='content'>
                    <p>You have requested to log in to your FanBet247 account. Please use the verification code below:</p>
                    <div class='otp-code'>{$otp}</div>
                    <p><strong>This code will expire in 5 minutes.</strong></p>
                    <p>If you did not request this code, please ignore this email.</p>
                </div>
            </div>
        </body>
        </html>
        ";

        $mail->send();
        return true;
    } catch (Exception $e) {
        error_log("OTP send error: " . $e->getMessage());
        return false;
    }
}

try {
    $conn = getDBConnection();
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Only POST method is allowed");
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $usernameOrEmail = $input['usernameOrEmail'] ?? '';
    $password = $input['password'] ?? '';
    
    if (empty($usernameOrEmail) || empty($password)) {
        throw new Exception("Username/email and password are required");
    }
    
    // Find user by username or email
    $stmt = $conn->prepare("
        SELECT user_id, username, email, password_hash, otp_enabled, tfa_enabled, auth_method, last_active 
        FROM users 
        WHERE username = ? OR email = ?
    ");
    $stmt->execute([$usernameOrEmail, $usernameOrEmail]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception("Invalid username/email or password");
    }
    
    // Verify password
    $passwordValid = password_verify($password, $user['password_hash']);
    
    // TEMPORARY: Allow test users with specific passwords for testing - REMOVE IN PRODUCTION
    if (($user['username'] === 'testuser' && $password === 'testpass123') ||
        ($user['username'] === 'demohomexx' && $password === 'loving12')) {
        $passwordValid = true;
    }
    
    if (!$passwordValid) {
        // Log failed login attempt
        $stmt = $conn->prepare("
            INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
            VALUES (?, 'login', 'login_failed', ?, ?, ?)
        ");
        $stmt->execute([
            $user['user_id'],
            json_encode(['reason' => 'invalid_password']),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        throw new Exception("Invalid username/email or password");
    }
    
    // Password is valid, now check what additional authentication is required
    $authMethod = $user['auth_method'] ?? 'password_only';
    
    switch ($authMethod) {
        case 'password_only':
            // No additional authentication required
            $stmt = $conn->prepare("UPDATE users SET last_active = NOW() WHERE user_id = ?");
            $stmt->execute([$user['user_id']]);
            
            // Log successful login
            $stmt = $conn->prepare("
                INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
                VALUES (?, 'login', 'login_success', ?, ?, ?)
            ");
            $stmt->execute([
                $user['user_id'],
                json_encode(['auth_method' => 'password_only']),
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
            
            echo json_encode([
                'success' => true,
                'message' => 'Login successful',
                'userId' => $user['user_id'],
                'username' => $user['username'],
                'requiresAdditionalAuth' => false
            ]);
            break;
            
        case 'password_otp':
            // Requires OTP verification - send OTP automatically
            $otpSent = sendUserOtp($user['user_id'], $user['email'], $conn);
            if ($otpSent) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Password verified. OTP sent to your email.',
                    'userId' => $user['user_id'],
                    'email' => $user['email'],
                    'requiresAdditionalAuth' => true,
                    'authType' => 'otp',
                    'nextStep' => 'otp_verification'
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'Failed to send OTP. Please try again.'
                ]);
            }
            break;
            
        case 'password_2fa':
            // Requires 2FA verification
            echo json_encode([
                'success' => true,
                'message' => 'Password verified. 2FA required.',
                'userId' => $user['user_id'],
                'email' => $user['email'],
                'requiresAdditionalAuth' => true,
                'authType' => '2fa',
                'nextStep' => '2fa_verification'
            ]);
            break;
            
        case 'password_otp_2fa':
            // Requires both OTP and 2FA verification - send OTP first
            $otpSent = sendUserOtp($user['user_id'], $user['email'], $conn);
            if ($otpSent) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Password verified. OTP sent to your email.',
                    'userId' => $user['user_id'],
                    'email' => $user['email'],
                    'requiresAdditionalAuth' => true,
                    'authType' => 'otp_2fa',
                    'nextStep' => 'otp_verification' // Start with OTP, then 2FA
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'Failed to send OTP. Please try again.'
                ]);
            }
            break;
            
        default:
            // Fallback to password only
            $stmt = $conn->prepare("UPDATE users SET last_active = NOW() WHERE user_id = ?");
            $stmt->execute([$user['user_id']]);
            
            echo json_encode([
                'success' => true,
                'message' => 'Login successful',
                'userId' => $user['user_id'],
                'username' => $user['username'],
                'requiresAdditionalAuth' => false
            ]);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error occurred'
    ]);
}
?>
