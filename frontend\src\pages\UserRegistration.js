import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useCurrency } from '../contexts/CurrencyContext';
import UserAuthLayout from '../components/UserAuthLayout';
import { userService, currencyService, apiService } from '../services';
import useApiService from '../hooks/useApiService';
import axios from '../utils/axiosConfig';
import { API_BASE_URL } from '../config';
import '../styles/UserAuth.css';
import '../styles/AuthAnimations.css';

function UserRegistration() {
    const [teams, setTeams] = useState([]);
    const [newUser, setNewUser] = useState({
        username: '',
        full_name: '',
        email: '',
        password: '',
        favorite_team: '',
        preferred_currency_id: '' // Require user selection
    });
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();

    // Currency system integration
    const { currencies, loading: currenciesLoading, error: currencyError } = useCurrency();
    const { execute } = useApiService();

    useEffect(() => {
        fetchTeams();
    }, []);

    const fetchTeams = async () => {
        try {
            const response = await axios.get('/handlers/team_management.php');
            console.log('Teams API response:', response.data); // Debug log
            if (response.data && response.data.data) {
                setTeams(response.data.data);
            } else if (response.data && Array.isArray(response.data)) {
                setTeams(response.data);
            } else {
                console.error('Unexpected teams response format:', response.data);
                setError('Failed to fetch teams - unexpected response format');
            }
        } catch (err) {
            console.error('Error fetching teams:', err);
            setError('Failed to fetch teams');
        }
    };

    // Handle currency error from context
    useEffect(() => {
        if (currencyError) {
            setError('Failed to load currencies. Please refresh the page.');
        }
    }, [currencyError]);

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setNewUser(prev => ({ ...prev, [name]: value }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setSuccess('');

        // Validate currency selection
        if (!newUser.preferred_currency_id) {
            setError('Please select your preferred currency');
            return;
        }

        setLoading(true);

        try {
            const response = await axios.post('/handlers/user_registration.php', newUser);
            console.log('Registration API response:', response.data); // Debug log

            if (response.data && response.data.success) {
                const selectedCurrency = currencies.find(c => c.id === parseInt(newUser.preferred_currency_id));
                setSuccess(
                    `Registration successful! Your preferred currency is set to ${selectedCurrency?.currency_name || selectedCurrency?.currency_code || 'USD'}. Please check your email for verification instructions.`
                );
                setTimeout(() => navigate('/login'), 3000);
            } else {
                setError(response.data?.message || 'Registration failed');
            }
        } catch (err) {
            console.error('Registration error:', err);
            setError(err.response?.data?.message || err.message || 'Registration failed');
        } finally {
            setLoading(false);
        }
    };

    return (
        <UserAuthLayout
            title="Create Account"
            subtitle="Join the FanBet247 community today"
            variant="registration"
        >
            <form onSubmit={handleSubmit} className="user-auth-form">
                    <div className="user-auth-form-group">
                        <label htmlFor="username">Username</label>
                        <div className="user-auth-input-wrapper">
                            <input
                                id="username"
                                type="text"
                                name="username"
                                value={newUser.username}
                                onChange={handleInputChange}
                                placeholder="Choose a unique username"
                                required
                                minLength="3"
                                maxLength="20"
                            />
                            <i className="fas fa-user"></i>
                        </div>
                    </div>

                    <div className="user-auth-form-group">
                        <label htmlFor="full_name">Full Name</label>
                        <div className="user-auth-input-wrapper">
                            <input
                                id="full_name"
                                type="text"
                                name="full_name"
                                value={newUser.full_name}
                                onChange={handleInputChange}
                                placeholder="Enter your full name"
                                required
                                minLength="2"
                                maxLength="50"
                            />
                            <i className="fas fa-id-card"></i>
                        </div>
                    </div>

                    <div className="user-auth-form-group">
                        <label htmlFor="email">Email Address</label>
                        <div className="user-auth-input-wrapper">
                            <input
                                id="email"
                                type="email"
                                name="email"
                                value={newUser.email}
                                onChange={handleInputChange}
                                placeholder="Enter your email address"
                                required
                            />
                            <i className="fas fa-envelope"></i>
                        </div>
                    </div>

                    <div className="user-auth-form-group">
                        <label htmlFor="password">Password</label>
                        <div className="user-auth-input-wrapper">
                            <input
                                id="password"
                                type="password"
                                name="password"
                                value={newUser.password}
                                onChange={handleInputChange}
                                placeholder="Create a strong password"
                                required
                                minLength="6"
                            />
                            <i className="fas fa-lock"></i>
                        </div>
                        <small className="form-help-text">
                            Password must be at least 6 characters long
                        </small>
                    </div>

                    <div className="user-auth-form-group">
                        <label htmlFor="favorite_team">Favorite Team</label>
                        <div className="user-auth-input-wrapper">
                            <select
                                id="favorite_team"
                                name="favorite_team"
                                value={newUser.favorite_team}
                                onChange={handleInputChange}
                                required
                            >
                                <option value="">Select your favorite team</option>
                                {teams.map(team => (
                                    <option key={team.id} value={team.name}>{team.name}</option>
                                ))}
                            </select>
                            <i className="fas fa-futbol"></i>
                        </div>
                    </div>

                    <div className="user-auth-form-group">
                        <label htmlFor="preferred_currency_id">Preferred Currency</label>
                        <div className="user-auth-input-wrapper">
                            <select
                                id="preferred_currency_id"
                                name="preferred_currency_id"
                                value={newUser.preferred_currency_id}
                                onChange={handleInputChange}
                                required
                                disabled={currenciesLoading}
                            >
                                {currenciesLoading ? (
                                    <option value="">Loading currencies...</option>
                                ) : (
                                    <>
                                        <option value="">Select your preferred currency</option>
                                        {currencies.map(currency => (
                                            <option key={currency.id} value={currency.id}>
                                                {currency.currency_code} - {currency.currency_name} ({currency.currency_symbol})
                                            </option>
                                        ))}
                                        {currencies.length === 0 && (
                                            <option value="1">USD - US Dollar (Default)</option>
                                        )}
                                    </>
                                )}
                            </select>
                            <i className="fas fa-coins"></i>
                        </div>
                        <small className="form-help-text">
                            <strong>Required:</strong> This will be used to display FanCoin amounts in your local currency.
                            You can change this later in your profile settings.
                        </small>
                    </div>

                {error && <div className="user-auth-error-message">{error}</div>}
                {success && <div className="user-auth-success-message">{success}</div>}

                <button
                    type="submit"
                    className="user-auth-button"
                    disabled={loading || currenciesLoading}
                >
                    {loading ? (
                        <>
                            <span className="user-auth-loading-spinner"></span>
                            Creating Account...
                        </>
                    ) : (
                        'Create Account'
                    )}
                </button>

                <div className="user-auth-footer">
                    <p>Already have an account? <Link to="/login" className="user-auth-link">Sign in here</Link></p>
                </div>
            </form>
        </UserAuthLayout>
    );
}

export default UserRegistration;
