/* 
 * Enhanced User Authentication Styles
 * Modern design system for FanBet247 user authentication
 */

/* Enhanced Form Styles for Full-Width Design */
.user-auth-form {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    max-width: 100%;
}

.user-auth-form-group {
    position: relative;
    width: 100%;
}

/* Improved spacing for full-width layout */
.user-auth-form-group:first-child {
    margin-top: 0;
}

.user-auth-form-group:last-child {
    margin-bottom: 0;
}

.user-auth-form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--gray-700);
    font-size: 0.875rem;
    font-weight: 500;
}

/* Enhanced Input Wrapper with Right-Side Icon (Admin Pattern) */
.user-auth-input-wrapper {
    position: relative;
    width: 100%;
}

.user-auth-input-wrapper input,
.user-auth-input-wrapper select {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 0.9375rem;
    font-weight: 400;
    line-height: 1.5;
    color: var(--gray-900);
    background: var(--white);
    transition: var(--transition-normal);
    box-sizing: border-box;
}

.user-auth-input-wrapper input:focus,
.user-auth-input-wrapper select:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(44, 95, 45, 0.1);
}

.user-auth-input-wrapper input::placeholder {
    color: var(--gray-400);
    font-weight: 400;
}

.user-auth-input-wrapper input:disabled,
.user-auth-input-wrapper select:disabled {
    background-color: var(--gray-50);
    color: var(--gray-500);
    cursor: not-allowed;
    border-color: var(--gray-200);
}

.user-auth-input-wrapper i {
    position: absolute;
    top: 50%;
    right: 1rem;
    transform: translateY(-50%);
    color: var(--gray-500);
    font-size: 1rem;
    pointer-events: none;
    transition: var(--transition-normal);
}

.user-auth-input-wrapper input:focus + i,
.user-auth-input-wrapper select:focus + i {
    color: var(--primary-green);
}

/* Simplified Button Styles */
.user-auth-button {
    width: 100%;
    padding: 0.75rem 1.5rem;
    background: var(--primary-green);
    color: var(--white);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.9375rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.user-auth-button:hover {
    background: var(--secondary-green);
}

.user-auth-button:active {
    background: var(--primary-green);
}

.user-auth-button:disabled {
    background: var(--gray-300);
    color: var(--gray-600);
    cursor: not-allowed;
}

/* Secondary Button */
.user-auth-button-secondary {
    width: 100%;
    padding: 0.75rem 1.5rem;
    background: transparent !important;
    color: var(--primary-green, #2C5F2D) !important;
    border: 1px solid var(--gray-300, #D1D5DB) !important;
    border-radius: var(--radius-md);
    font-size: 0.9375rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.user-auth-button-secondary:hover {
    background: var(--gray-50);
    border-color: var(--primary-green);
}

/* Form Options */
.user-auth-form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1rem 0;
}

.user-auth-form-options.center {
    justify-content: center;
}

.user-auth-form-options.end {
    justify-content: flex-end;
}

/* Help Text Styling */
.form-help-text {
    font-size: 0.8125rem;
    color: var(--gray-600);
    margin-top: 0.375rem;
    line-height: 1.4;
}

/* Enhanced Links */
.user-auth-link,
.user-auth-forgot-password,
.user-auth-register-link {
    color: var(--primary-green);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 600;
    transition: var(--transition-normal);
    position: relative;
}

.user-auth-link::after,
.user-auth-forgot-password::after,
.user-auth-register-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--secondary-green);
    transition: var(--transition-normal);
}

.user-auth-link:hover,
.user-auth-forgot-password:hover,
.user-auth-register-link:hover {
    color: var(--secondary-green);
}

.user-auth-link:hover::after,
.user-auth-forgot-password:hover::after,
.user-auth-register-link:hover::after {
    width: 100%;
}

/* Footer */
.user-auth-footer {
    margin-top: 2rem;
    text-align: center;
}

.user-auth-footer p {
    color: var(--gray-600);
    font-size: 0.875rem;
    margin: 0;
    line-height: 1.6;
}

/* Enhanced Messages */
.user-auth-error-message,
.user-auth-success-message {
    padding: 1rem;
    border-radius: var(--radius-lg);
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: slideInDown 0.3s ease-out;
}

.user-auth-error-message {
    background: #FEF2F2;
    color: #DC2626;
    border: 1px solid #FECACA;
}

.user-auth-error-message::before {
    content: '\f071';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    color: #DC2626;
}

.user-auth-success-message {
    background: #F0FDF4;
    color: #166534;
    border: 1px solid #BBF7D0;
}

.user-auth-success-message::before {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    color: #166534;
}

/* Loading States */
.user-auth-loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Enhanced OTP Input */
.user-auth-otp-input {
    text-align: center;
    letter-spacing: 0.5rem;
    font-size: 1.25rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
}

/* Countdown Timer */
.user-auth-countdown-timer {
    font-size: 0.875rem;
    color: var(--gray-600);
    text-align: center;
    margin-top: 0.75rem;
    padding: 0.5rem;
    background: var(--gray-50);
    border-radius: var(--radius-md);
}

.user-auth-countdown-timer.expired {
    color: var(--error);
    background: #FEF2F2;
}

/* Resend Button */
.user-auth-resend-button {
    background: none !important;
    border: none !important;
    color: var(--primary-green, #2C5F2D) !important;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    text-decoration: underline;
    transition: var(--transition-normal);
    padding: 0.5rem;
    border-radius: var(--radius-md);
}

.user-auth-resend-button:hover {
    color: var(--secondary-green);
    background: rgba(44, 95, 45, 0.05);
}

.user-auth-resend-button:disabled {
    color: var(--gray-500, #6B7280) !important;
    cursor: not-allowed;
    text-decoration: none;
    background: none !important;
}

/* Animations */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Form Validation States */
.user-auth-form-group.success .user-auth-input-wrapper input {
    border-color: var(--success);
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
}

.user-auth-form-group.error .user-auth-input-wrapper input {
    border-color: var(--error);
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
    animation: shake 0.3s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Mobile-First Responsive Design */
@media screen and (max-width: 768px) {
    .user-auth-form {
        gap: 1.25rem;
    }

    .user-auth-form-options {
        flex-direction: column;
        gap: 0.75rem;
        align-items: center;
    }

    .user-auth-input-wrapper input,
    .user-auth-input-wrapper select {
        padding: 0.6875rem 0.875rem 0.6875rem 2.5rem;
        font-size: 0.9375rem;
    }

    .user-auth-input-wrapper i {
        left: 0.75rem;
        font-size: 0.8125rem;
    }

    .user-auth-button,
    .user-auth-button-secondary {
        padding: 0.6875rem 1.25rem;
        font-size: 0.9375rem;
    }
}

@media screen and (max-width: 480px) {
    .user-auth-form {
        gap: 1rem;
    }

    .user-auth-form-group label {
        font-size: 0.8125rem;
        margin-bottom: 0.375rem;
    }

    .user-auth-input-wrapper input,
    .user-auth-input-wrapper select {
        padding: 0.625rem 0.75rem 0.625rem 2.25rem;
        font-size: 0.875rem;
    }

    .user-auth-input-wrapper i {
        left: 0.6875rem;
        font-size: 0.75rem;
    }

    .user-auth-button,
    .user-auth-button-secondary {
        padding: 0.625rem 1rem;
        font-size: 0.875rem;
    }

    .form-help-text {
        font-size: 0.75rem;
        margin-top: 0.25rem;
    }
}

/* Notifications */
.user-auth-notification {
    margin-bottom: 1.5rem;
    border-radius: var(--radius-md);
    padding: 1rem;
    animation: slideInFromTop 0.3s ease-out;
}

.user-auth-notification-success {
    background-color: #f0f9ff;
    border: 1px solid #22c55e;
    color: #15803d;
}

.user-auth-notification-error {
    background-color: #fef2f2;
    border: 1px solid #ef4444;
    color: #dc2626;
}

.user-auth-notification-info {
    background-color: #eff6ff;
    border: 1px solid #3b82f6;
    color: #1d4ed8;
}

.user-auth-notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-auth-notification-icon {
    font-weight: bold;
    font-size: 1.125rem;
    flex-shrink: 0;
}

.user-auth-notification-message {
    flex: 1;
    font-weight: 500;
}

.user-auth-notification-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0;
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
    color: inherit;
    flex-shrink: 0;
}

.user-auth-notification-close:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
