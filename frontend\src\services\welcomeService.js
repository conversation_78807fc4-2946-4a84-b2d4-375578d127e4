/**
 * Welcome Page Service - FanBet247
 * Handles all API calls for the welcome/home page data
 */

import { API_BASE_URL } from '../config';

class WelcomeService {
    constructor() {
        this.baseURL = API_BASE_URL;
    }

    /**
     * Generic API request handler with error handling
     * @param {string} endpoint - API endpoint
     * @param {object} options - Fetch options
     * @returns {Promise<object>} API response
     */
    async apiRequest(endpoint, options = {}) {
        try {
            const url = `${this.baseURL}/${endpoint}`;
            console.log('🌐 API Request:', url);

            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                ...options
            };

            const response = await fetch(url, defaultOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('✅ API Response:', data);
            return data;
        } catch (error) {
            console.error('❌ API Error:', error);
            throw error;
        }
    }

    /**
     * Fetch recent bets for welcome page
     * @param {number} limit - Number of bets to fetch
     * @returns {Promise<object>} Recent bets data
     */
    async getRecentBets(limit = 6) {
        try {
            const data = await this.apiRequest(`handlers/welcome_recent_bets.php?limit=${limit}`);
            return {
                success: data.success || false,
                bets: data.bets || [],
                message: data.message || ''
            };
        } catch (error) {
            console.error('Error fetching recent bets:', error);
            return {
                success: false,
                bets: [],
                message: 'Failed to fetch recent bets'
            };
        }
    }

    /**
     * Fetch live matches/challenges for betting
     * @param {number} limit - Number of matches to fetch
     * @returns {Promise<object>} Live matches data
     */
    async getLiveMatches(limit = 6) {
        try {
            const data = await this.apiRequest('handlers/challenge_system.php');
            const matches = data.challenges || [];
            
            // Filter and limit live matches
            const liveMatches = matches
                .filter(match => match.status === 'Open' && match.seconds_remaining > 0)
                .slice(0, limit)
                .map(match => ({
                    ...match,
                    isLive: true,
                    timeRemaining: match.seconds_remaining,
                    team_a_logo: match.team_a_logo || this.getDefaultTeamLogo(match.team_a),
                    team_b_logo: match.team_b_logo || this.getDefaultTeamLogo(match.team_b)
                }));

            return {
                success: data.success || false,
                matches: liveMatches,
                message: data.message || ''
            };
        } catch (error) {
            console.error('Error fetching live matches:', error);
            return {
                success: false,
                matches: [],
                message: 'Failed to fetch live matches'
            };
        }
    }

    /**
     * Fetch recent challenges for the challenges section
     * @param {number} limit - Number of challenges to fetch
     * @returns {Promise<object>} Recent challenges data
     */
    async getRecentChallenges(limit = 4) {
        try {
            const data = await this.apiRequest('api/get_recent_data.php');
            const challenges = data.data?.recentChallenges || [];
            
            return {
                success: data.status === 'success',
                challenges: challenges.slice(0, limit),
                message: data.message || ''
            };
        } catch (error) {
            console.error('Error fetching recent challenges:', error);
            return {
                success: false,
                challenges: [],
                message: 'Failed to fetch recent challenges'
            };
        }
    }

    /**
     * Fetch top leagues for sidebar
     * @param {number} limit - Number of leagues to fetch
     * @returns {Promise<object>} Top leagues data
     */
    async getTopLeagues(limit = 7) {
        try {
            const data = await this.apiRequest('handlers/get_leagues.php');
            const leagues = data.data || [];
            
            // Sort by member count and take top leagues
            const topLeagues = leagues
                .sort((a, b) => (b.member_count || 0) - (a.member_count || 0))
                .slice(0, limit)
                .map(league => ({
                    ...league,
                    icon_url: league.icon_url || this.getDefaultLeagueIcon(league.name),
                    match_count: league.member_count || 0
                }));

            return {
                success: data.status === 200,
                leagues: topLeagues,
                message: data.message || ''
            };
        } catch (error) {
            console.error('Error fetching top leagues:', error);
            return {
                success: false,
                leagues: this.getDefaultLeagues(),
                message: 'Failed to fetch leagues, showing defaults'
            };
        }
    }

    /**
     * Fetch user balance if logged in
     * @param {number} userId - User ID
     * @returns {Promise<object>} User balance data
     */
    async getUserBalance(userId) {
        if (!userId) {
            return { success: false, balance: 0 };
        }

        try {
            const data = await this.apiRequest(`handlers/user_data.php?user_id=${userId}`);
            return {
                success: data.success || false,
                balance: data.user?.balance || 0,
                user: data.user || null
            };
        } catch (error) {
            console.error('Error fetching user balance:', error);
            return {
                success: false,
                balance: 0,
                user: null
            };
        }
    }

    /**
     * Fetch all welcome page data in one call
     * @param {object} options - Options for data fetching
     * @returns {Promise<object>} Combined welcome page data
     */
    async getWelcomePageData(options = {}) {
        const {
            betsLimit = 6,
            matchesLimit = 6,
            challengesLimit = 4,
            leaguesLimit = 7,
            userId = null
        } = options;

        try {
            console.log('🏠 Fetching welcome page data...');

            // Fetch all data in parallel for better performance
            const [
                recentBetsData,
                liveMatchesData,
                recentChallengesData,
                topLeaguesData,
                userBalanceData
            ] = await Promise.allSettled([
                this.getRecentBets(betsLimit),
                this.getLiveMatches(matchesLimit),
                this.getRecentChallenges(challengesLimit),
                this.getTopLeagues(leaguesLimit),
                userId ? this.getUserBalance(userId) : Promise.resolve({ success: false, balance: 0 })
            ]);

            // Extract data from settled promises
            const extractData = (result) => result.status === 'fulfilled' ? result.value : { success: false };

            return {
                success: true,
                data: {
                    recentBets: extractData(recentBetsData),
                    liveMatches: extractData(liveMatchesData),
                    recentChallenges: extractData(recentChallengesData),
                    topLeagues: extractData(topLeaguesData),
                    userBalance: extractData(userBalanceData)
                },
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error fetching welcome page data:', error);
            return {
                success: false,
                data: {
                    recentBets: { success: false, bets: [] },
                    liveMatches: { success: false, matches: [] },
                    recentChallenges: { success: false, challenges: [] },
                    topLeagues: { success: false, leagues: this.getDefaultLeagues() },
                    userBalance: { success: false, balance: 0 }
                },
                error: error.message
            };
        }
    }

    /**
     * Get default team logo URL
     * @param {string} teamName - Team name
     * @returns {string} Default logo URL
     */
    getDefaultTeamLogo(teamName) {
        if (!teamName) return '/images/default-team.png';
        
        // Try to construct logo URL from team name
        const cleanName = teamName.toLowerCase().replace(/\s+/g, '_');
        return `${this.baseURL}/uploads/teams/${cleanName}.png`;
    }

    /**
     * Get default league icon URL
     * @param {string} leagueName - League name
     * @returns {string} Default icon URL
     */
    getDefaultLeagueIcon(leagueName) {
        const leagueIcons = {
            'Premier League': 'https://media.api-sports.io/football/leagues/39.png',
            'La Liga': 'https://media.api-sports.io/football/leagues/140.png',
            'Bundesliga': 'https://media.api-sports.io/football/leagues/78.png',
            'Serie A': 'https://media.api-sports.io/football/leagues/135.png',
            'Ligue 1': 'https://media.api-sports.io/football/leagues/61.png',
            'Champions League': 'https://media.api-sports.io/football/leagues/1.png',
            'Brasileirão': 'https://media.api-sports.io/football/leagues/71.png'
        };
        
        return leagueIcons[leagueName] || '/images/default-league.png';
    }

    /**
     * Get default leagues data for fallback
     * @returns {Array} Default leagues array
     */
    getDefaultLeagues() {
        return [
            { league_id: 1, name: 'Premier League', member_count: 24, icon_url: this.getDefaultLeagueIcon('Premier League') },
            { league_id: 2, name: 'La Liga', member_count: 18, icon_url: this.getDefaultLeagueIcon('La Liga') },
            { league_id: 3, name: 'Bundesliga', member_count: 16, icon_url: this.getDefaultLeagueIcon('Bundesliga') },
            { league_id: 4, name: 'Serie A', member_count: 14, icon_url: this.getDefaultLeagueIcon('Serie A') },
            { league_id: 5, name: 'Ligue 1', member_count: 12, icon_url: this.getDefaultLeagueIcon('Ligue 1') },
            { league_id: 6, name: 'Champions League', member_count: 8, icon_url: this.getDefaultLeagueIcon('Champions League') },
            { league_id: 7, name: 'Brasileirão', member_count: 10, icon_url: this.getDefaultLeagueIcon('Brasileirão') }
        ];
    }

    /**
     * Simulate live odds updates
     * @param {Array} matches - Array of matches to update
     * @returns {Array} Updated matches with new odds
     */
    updateLiveOdds(matches) {
        return matches.map(match => ({
            ...match,
            odds_team_a: this.adjustOdds(match.odds_team_a),
            odds_team_b: this.adjustOdds(match.odds_team_b),
            odds_draw: this.adjustOdds(match.odds_draw),
            lastUpdated: new Date().toISOString()
        }));
    }

    /**
     * Adjust odds with small random variations
     * @param {string|number} odds - Current odds
     * @returns {string} Adjusted odds
     */
    adjustOdds(odds) {
        const currentOdds = parseFloat(odds) || 2.0;
        const variation = (Math.random() - 0.5) * 0.3; // ±0.15 variation
        const newOdds = Math.max(1.01, currentOdds + variation);
        return newOdds.toFixed(2);
    }
}

// Create and export singleton instance
const welcomeService = new WelcomeService();
export default welcomeService;
