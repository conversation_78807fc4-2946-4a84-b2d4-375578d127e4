<?php
require_once 'backend/includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Check if teams table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'teams'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo "❌ Teams table does not exist\n";
        exit;
    }
    
    echo "✅ Teams table exists\n";
    
    // Count teams
    $stmt = $conn->query('SELECT COUNT(*) as count FROM teams');
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Teams count: " . $result['count'] . "\n";
    
    // Get sample teams
    $stmt = $conn->query('SELECT id, name FROM teams LIMIT 10');
    $teams = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($teams)) {
        echo "❌ No teams found in database\n";
    } else {
        echo "✅ Sample teams:\n";
        foreach ($teams as $team) {
            echo "  - ID: {$team['id']}, Name: {$team['name']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
