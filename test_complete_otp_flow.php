<?php
header('Content-Type: text/plain');

echo "🧪 COMPLETE OTP AUTHENTICATION FLOW TEST\n";
echo "=========================================\n\n";

require_once 'backend/includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Test user credentials
    $testUsername = 'demohomexx';
    $testPassword = 'loving12';
    
    echo "📋 Step 1: Testing Enhanced Login Handler\n";
    echo "-----------------------------------------\n";
    
    // Simulate the enhanced login request
    $_POST = [
        'username' => $testUsername,
        'password' => $testPassword
    ];
    
    // Capture the output from the enhanced login handler
    ob_start();
    include 'backend/handlers/user_login_enhanced.php';
    $loginResponse = ob_get_clean();
    
    echo "Login Response:\n";
    echo $loginResponse . "\n\n";
    
    $loginData = json_decode($loginResponse, true);
    
    if ($loginData && $loginData['success']) {
        if (isset($loginData['requiresAdditionalAuth']) && $loginData['requiresAdditionalAuth']) {
            echo "✅ Login successful - Additional auth required\n";
            echo "   Auth Type: {$loginData['authType']}\n";
            echo "   User ID: {$loginData['userId']}\n";
            echo "   Email: {$loginData['email']}\n\n";
            
            // Check if OTP was sent
            echo "📋 Step 2: Checking if OTP was sent\n";
            echo "-----------------------------------\n";
            
            $stmt = $conn->prepare("SELECT * FROM user_otp WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
            $stmt->execute([$loginData['userId']]);
            $otpRecord = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($otpRecord) {
                echo "✅ OTP record found:\n";
                echo "   OTP Code: {$otpRecord['otp']}\n";
                echo "   Expiry: {$otpRecord['expiry']}\n";
                echo "   Used: " . ($otpRecord['used'] ? 'Yes' : 'No') . "\n\n";
                
                // Test OTP verification
                echo "📋 Step 3: Testing OTP Verification\n";
                echo "-----------------------------------\n";
                
                $_POST = [
                    'userId' => $loginData['userId'],
                    'otp_code' => $otpRecord['otp']
                ];
                
                ob_start();
                include 'backend/handlers/user_verify_otp.php';
                $verifyResponse = ob_get_clean();
                
                echo "OTP Verification Response:\n";
                echo $verifyResponse . "\n\n";
                
                $verifyData = json_decode($verifyResponse, true);
                
                if ($verifyData && $verifyData['success']) {
                    echo "✅ OTP verification successful!\n";
                    if (isset($verifyData['token'])) {
                        echo "   Auth Token: {$verifyData['token']}\n";
                    }
                    if (isset($verifyData['requiresAdditionalAuth'])) {
                        echo "   Requires Additional Auth: " . ($verifyData['requiresAdditionalAuth'] ? 'Yes' : 'No') . "\n";
                    }
                } else {
                    echo "❌ OTP verification failed\n";
                    if (isset($verifyData['message'])) {
                        echo "   Error: {$verifyData['message']}\n";
                    }
                }
                
            } else {
                echo "❌ No OTP record found - OTP was not sent\n";
            }
            
        } else {
            echo "❌ Login successful but no additional auth required\n";
            echo "   This means OTP is not properly configured for this user\n";
        }
    } else {
        echo "❌ Login failed\n";
        if (isset($loginData['message'])) {
            echo "   Error: {$loginData['message']}\n";
        }
    }
    
    echo "\n🎉 OTP FLOW TEST COMPLETE!\n\n";
    
    echo "📝 SUMMARY:\n";
    echo "----------\n";
    echo "1. Enhanced login handler: " . ($loginData && $loginData['success'] ? '✅ Working' : '❌ Failed') . "\n";
    echo "2. OTP sending: " . (isset($otpRecord) && $otpRecord ? '✅ Working' : '❌ Failed') . "\n";
    echo "3. OTP verification: " . (isset($verifyData) && $verifyData && $verifyData['success'] ? '✅ Working' : '❌ Failed') . "\n\n";
    
    echo "📱 FRONTEND TESTING:\n";
    echo "-------------------\n";
    echo "1. Open the frontend login page\n";
    echo "2. Login with username: {$testUsername}\n";
    echo "3. Use password: {$testPassword}\n";
    echo "4. System should show OTP verification form\n";
    echo "5. Check email for OTP code or use: " . (isset($otpRecord['otp']) ? $otpRecord['otp'] : 'N/A') . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
