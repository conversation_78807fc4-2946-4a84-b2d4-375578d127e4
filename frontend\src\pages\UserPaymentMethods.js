import React, { useState, useEffect } from 'react';
import axios from '../utils/axiosConfig';
import './UserPaymentMethods.css';

const UserPaymentMethods = () => {
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);

  const [newMethod, setNewMethod] = useState({
    method_type: '',
    method_name: '',
    account_details: {},
    is_primary: false
  });

  useEffect(() => {
    fetchPaymentMethods();
  }, []);

  const fetchPaymentMethods = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/handlers/user_payment_methods.php');
      if (response.data.success) {
        setPaymentMethods(response.data.data);
      } else {
        setError('Failed to load payment methods');
      }
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      setError('Failed to load payment methods');
    } finally {
      setLoading(false);
    }
  };

  const handleAddMethod = async (e) => {
    e.preventDefault();
    
    if (!newMethod.method_type || !newMethod.method_name || Object.keys(newMethod.account_details).length === 0) {
      setError('Please fill in all required fields');
      return;
    }

    try {
      const response = await axios.post('/handlers/user_payment_methods.php', newMethod);
      if (response.data.success) {
        setSuccess('Payment method added successfully');
        setShowAddModal(false);
        setNewMethod({
          method_type: '',
          method_name: '',
          account_details: {},
          is_primary: false
        });
        fetchPaymentMethods();
      } else {
        setError(response.data.message || 'Failed to add payment method');
      }
    } catch (error) {
      console.error('Error adding payment method:', error);
      setError('Failed to add payment method');
    }
  };

  const handleDeleteMethod = async (paymentMethodId) => {
    if (!window.confirm('Are you sure you want to delete this payment method?')) {
      return;
    }

    try {
      const response = await axios.delete(`/handlers/user_payment_methods.php?payment_method_id=${paymentMethodId}`);
      if (response.data.success) {
        setSuccess('Payment method deleted successfully');
        fetchPaymentMethods();
      } else {
        setError(response.data.message || 'Failed to delete payment method');
      }
    } catch (error) {
      console.error('Error deleting payment method:', error);
      setError('Failed to delete payment method');
    }
  };

  const handleSetPrimary = async (paymentMethodId) => {
    try {
      const response = await axios.put('/handlers/user_payment_methods.php', {
        payment_method_id: paymentMethodId,
        is_primary: true
      });
      if (response.data.success) {
        setSuccess('Primary payment method updated');
        fetchPaymentMethods();
      } else {
        setError(response.data.message || 'Failed to update primary method');
      }
    } catch (error) {
      console.error('Error updating primary method:', error);
      setError('Failed to update primary method');
    }
  };

  const handleMethodTypeChange = (type) => {
    setNewMethod(prev => ({
      ...prev,
      method_type: type,
      account_details: getDefaultDetailsForType(type)
    }));
  };

  const getDefaultDetailsForType = (type) => {
    switch (type) {
      case 'bank_account':
        return { account_number: '', bank_name: '', account_holder_name: '', routing_number: '' };
      case 'crypto_wallet':
        return { wallet_address: '', currency_type: '' };
      case 'paypal':
        return { email: '' };
      case 'mobile_money':
        return { phone_number: '', provider: '' };
      default:
        return {};
    }
  };

  const updateAccountDetail = (key, value) => {
    setNewMethod(prev => ({
      ...prev,
      account_details: {
        ...prev.account_details,
        [key]: value
      }
    }));
  };

  const renderAccountDetailsForm = () => {
    const { method_type, account_details } = newMethod;

    switch (method_type) {
      case 'bank_account':
        return (
          <>
            <div className="form-group">
              <label>Account Holder Name *</label>
              <input
                type="text"
                value={account_details.account_holder_name || ''}
                onChange={(e) => updateAccountDetail('account_holder_name', e.target.value)}
                required
              />
            </div>
            <div className="form-group">
              <label>Bank Name *</label>
              <input
                type="text"
                value={account_details.bank_name || ''}
                onChange={(e) => updateAccountDetail('bank_name', e.target.value)}
                required
              />
            </div>
            <div className="form-group">
              <label>Account Number *</label>
              <input
                type="text"
                value={account_details.account_number || ''}
                onChange={(e) => updateAccountDetail('account_number', e.target.value)}
                required
              />
            </div>
            <div className="form-group">
              <label>Routing Number</label>
              <input
                type="text"
                value={account_details.routing_number || ''}
                onChange={(e) => updateAccountDetail('routing_number', e.target.value)}
              />
            </div>
          </>
        );
      case 'crypto_wallet':
        return (
          <>
            <div className="form-group">
              <label>Wallet Address *</label>
              <input
                type="text"
                value={account_details.wallet_address || ''}
                onChange={(e) => updateAccountDetail('wallet_address', e.target.value)}
                required
              />
            </div>
            <div className="form-group">
              <label>Currency Type *</label>
              <select
                value={account_details.currency_type || ''}
                onChange={(e) => updateAccountDetail('currency_type', e.target.value)}
                required
              >
                <option value="">Select currency</option>
                <option value="BTC">Bitcoin (BTC)</option>
                <option value="ETH">Ethereum (ETH)</option>
                <option value="USDT">Tether (USDT)</option>
                <option value="USDC">USD Coin (USDC)</option>
              </select>
            </div>
          </>
        );
      case 'paypal':
        return (
          <div className="form-group">
            <label>PayPal Email *</label>
            <input
              type="email"
              value={account_details.email || ''}
              onChange={(e) => updateAccountDetail('email', e.target.value)}
              required
            />
          </div>
        );
      case 'mobile_money':
        return (
          <>
            <div className="form-group">
              <label>Phone Number *</label>
              <input
                type="tel"
                value={account_details.phone_number || ''}
                onChange={(e) => updateAccountDetail('phone_number', e.target.value)}
                required
              />
            </div>
            <div className="form-group">
              <label>Provider *</label>
              <select
                value={account_details.provider || ''}
                onChange={(e) => updateAccountDetail('provider', e.target.value)}
                required
              >
                <option value="">Select provider</option>
                <option value="M-Pesa">M-Pesa</option>
                <option value="Airtel Money">Airtel Money</option>
                <option value="MTN Mobile Money">MTN Mobile Money</option>
                <option value="Orange Money">Orange Money</option>
              </select>
            </div>
          </>
        );
      default:
        return null;
    }
  };

  const getStatusBadge = (method) => {
    const statusClass = `status-badge ${method.status}`;
    const statusText = method.status.replace('_', ' ').toUpperCase();
    return <span className={statusClass}>{statusText}</span>;
  };

  const formatAccountDetails = (method) => {
    const details = method.account_details;
    switch (method.method_type) {
      case 'bank_account':
        return `${details.bank_name} - ****${details.account_number?.slice(-4)}`;
      case 'crypto_wallet':
        return `${details.currency_type} - ${details.wallet_address?.slice(0, 6)}...${details.wallet_address?.slice(-4)}`;
      case 'paypal':
        return details.email;
      case 'mobile_money':
        return `${details.provider} - ${details.phone_number}`;
      default:
        return 'Unknown';
    }
  };

  if (loading) {
    return (
      <div className="user-payment-methods-container">
        <div className="loading">Loading payment methods...</div>
      </div>
    );
  }

  return (
    <div className="user-payment-methods-container">
      <div className="payment-methods-header">
        <h1>My Payment Methods</h1>
        <button 
          className="btn-primary"
          onClick={() => setShowAddModal(true)}
        >
          Add Payment Method
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}
      {success && <div className="success-message">{success}</div>}

      {paymentMethods.length === 0 ? (
        <div className="no-methods">
          <h3>No Payment Methods</h3>
          <p>Add a payment method to start withdrawing your FanCoins.</p>
        </div>
      ) : (
        <div className="methods-grid">
          {paymentMethods.map(method => (
            <div key={method.payment_method_id} className={`method-card ${method.is_primary ? 'primary' : ''}`}>
              <div className="method-header">
                <div className="method-info">
                  <h3>{method.method_name}</h3>
                  <span className="method-type">{method.method_type.replace('_', ' ')}</span>
                </div>
                {method.is_primary && <span className="primary-badge">PRIMARY</span>}
              </div>
              
              <div className="method-details">
                <p>{formatAccountDetails(method)}</p>
                {getStatusBadge(method)}
              </div>

              <div className="method-actions">
                {!method.is_primary && method.status === 'active' && (
                  <button 
                    className="btn-secondary"
                    onClick={() => handleSetPrimary(method.payment_method_id)}
                  >
                    Set Primary
                  </button>
                )}
                <button 
                  className="btn-danger"
                  onClick={() => handleDeleteMethod(method.payment_method_id)}
                >
                  Delete
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {showAddModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h2>Add Payment Method</h2>
              <button 
                className="modal-close"
                onClick={() => setShowAddModal(false)}
              >
                ×
              </button>
            </div>

            <form onSubmit={handleAddMethod} className="modal-body">
              <div className="form-group">
                <label>Method Type *</label>
                <select
                  value={newMethod.method_type}
                  onChange={(e) => handleMethodTypeChange(e.target.value)}
                  required
                >
                  <option value="">Select method type</option>
                  <option value="bank_account">Bank Account</option>
                  <option value="crypto_wallet">Crypto Wallet</option>
                  <option value="paypal">PayPal</option>
                  <option value="mobile_money">Mobile Money</option>
                </select>
              </div>

              <div className="form-group">
                <label>Method Name *</label>
                <input
                  type="text"
                  value={newMethod.method_name}
                  onChange={(e) => setNewMethod(prev => ({ ...prev, method_name: e.target.value }))}
                  placeholder="e.g., My Main Bank Account"
                  required
                />
              </div>

              {renderAccountDetailsForm()}

              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={newMethod.is_primary}
                    onChange={(e) => setNewMethod(prev => ({ ...prev, is_primary: e.target.checked }))}
                  />
                  Set as primary payment method
                </label>
              </div>

              <div className="modal-actions">
                <button type="button" className="btn-secondary" onClick={() => setShowAddModal(false)}>
                  Cancel
                </button>
                <button type="submit" className="btn-primary">
                  Add Method
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserPaymentMethods;
