{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\JoinChallenge2.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport axios from '../utils/axiosConfig';\nimport './JoinChallenge2.css';\nimport '../styles/TeamLogo.css';\n\n// Remove API_BASE_URL since axios is already configured with the correct base URL\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst JoinChallenge2 = () => {\n  _s();\n  const {\n    challengeId,\n    betId,\n    uniqueCode\n  } = useParams();\n  const navigate = useNavigate();\n  const [challenge, setChallenge] = useState(null);\n  const [bet, setBet] = useState(null);\n  const [betAmount, setBetAmount] = useState('');\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [currentUsername, setCurrentUsername] = useState('');\n  const [isLoading, setIsLoading] = useState(true);\n  const [potentialReturns, setPotentialReturns] = useState({\n    win: 0,\n    draw: 0,\n    loss: 0\n  });\n  const [teams, setTeams] = useState([]);\n  const userId = localStorage.getItem('userId');\n  const user1Id = bet === null || bet === void 0 ? void 0 : bet.user1_id;\n  const fetchChallenge = useCallback(async () => {\n    try {\n      console.log('Fetching challenge with ID:', challengeId);\n      const response = await axios.get(`/handlers/get_challenges.php`, {\n        params: {\n          id: challengeId\n        }\n      });\n      console.log('Challenge response:', response.data);\n      if (response.data.success) {\n        setChallenge(response.data.challenge);\n      } else {\n        throw new Error(response.data.message || 'Failed to fetch challenge');\n      }\n    } catch (error) {\n      console.error('Error fetching challenge:', error);\n      setError('Failed to load challenge details. Please try again.');\n    }\n  }, [challengeId]);\n  const fetchBetDetails = useCallback(async () => {\n    try {\n      console.log('Fetching bet details:', {\n        betId,\n        uniqueCode\n      });\n      const response = await axios.get(`/handlers/get_bet_details.php`, {\n        params: {\n          betId,\n          uniqueCode\n        }\n      });\n      console.log('Bet details response:', response.data);\n      if (response.data.success) {\n        setBet(response.data.bet);\n        // Set initial bet amount from user1's amount\n        setBetAmount(response.data.bet.amount_user1);\n      } else {\n        throw new Error(response.data.message || 'Failed to fetch bet details');\n      }\n    } catch (error) {\n      console.error('Error fetching bet details:', error);\n      setError('Error loading bet details. Please try again.');\n    }\n  }, [betId, uniqueCode]);\n  const fetchTeams = useCallback(async () => {\n    try {\n      const response = await axios.get(`/handlers/team_management.php`);\n      if (response.data.status === 200) {\n        setTeams(response.data.data);\n      } else {\n        console.warn('Team data not in expected format:', response.data);\n      }\n    } catch (error) {\n      console.error('Error fetching teams:', error);\n    }\n  }, []);\n  const fetchCurrentUsername = useCallback(async () => {\n    try {\n      if (!userId) {\n        throw new Error('User ID not found');\n      }\n      const response = await axios.get(`/handlers/user_data.php`, {\n        params: {\n          userId\n        }\n      });\n      if (response.data.success) {\n        setCurrentUsername(response.data.user.username);\n      } else {\n        throw new Error(response.data.message || 'Failed to fetch user data');\n      }\n    } catch (error) {\n      console.error('Error fetching current username:', error);\n      setError('Error loading user data');\n    }\n  }, [userId]);\n  const getTeamLogo = useCallback(teamName => {\n    const team = teams.find(t => t.name === teamName);\n    return team ? `/${team.logo}` : '/default-team-logo.png';\n  }, [teams]);\n  const calculatePotentialReturns = useCallback(() => {\n    if (!betAmount || !challenge || !bet) return;\n    try {\n      const amount = parseFloat(betAmount);\n      if (isNaN(amount)) return;\n      let winOdds;\n      // Determine odds based on user1's bet choice\n      if (bet.bet_choice_user1 === 'team_a_win') {\n        winOdds = parseFloat(challenge.odds_team_b);\n      } else if (bet.bet_choice_user1 === 'team_b_win') {\n        winOdds = parseFloat(challenge.odds_team_a);\n      } else {\n        winOdds = parseFloat(challenge.odds_draw);\n      }\n      const drawOdds = parseFloat(challenge.odds_draw) || 0.9;\n      const lossOdds = parseFloat(challenge.odds_lost) || 0.2;\n      setPotentialReturns({\n        win: (amount * winOdds).toFixed(2),\n        draw: (amount * drawOdds).toFixed(2),\n        loss: (amount * lossOdds).toFixed(2)\n      });\n    } catch (error) {\n      console.error('Error calculating returns:', error);\n    }\n  }, [betAmount, challenge, bet]);\n  const getTeamDisplay = useCallback(() => {\n    if (!challenge || !bet) return null;\n\n    // Determine teams based on user1's bet choice\n    const leftTeam = bet.bet_choice_user1 === 'team_b_win' ? challenge.team_b : challenge.team_a;\n    const leftTeamLogo = bet.bet_choice_user1 === 'team_b_win' ? getTeamLogo(challenge.team_b) : getTeamLogo(challenge.team_a);\n    const rightTeam = bet.bet_choice_user1 === 'team_b_win' ? challenge.team_a : challenge.team_b;\n    const rightTeamLogo = bet.bet_choice_user1 === 'team_b_win' ? getTeamLogo(challenge.team_a) : getTeamLogo(challenge.team_b);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"challenge-details\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"team-info\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"challenge-team-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: leftTeamLogo,\n            alt: leftTeam,\n            className: \"challenge-team-logo\",\n            onError: e => {\n              e.target.src = '/default-team-logo.png';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"challenge-team-name\",\n            children: leftTeam\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"player-name\",\n            children: bet.user1_name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"opponent-pick\",\n            children: \"Opponent's Pick\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"vs-container\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"vs\",\n          children: \"VS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"team-info\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"challenge-team-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: rightTeamLogo,\n            alt: rightTeam,\n            className: \"challenge-team-logo\",\n            onError: e => {\n              e.target.src = '/default-team-logo.png';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"challenge-team-name\",\n            children: rightTeam\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"player-name\",\n            children: currentUsername\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this);\n  }, [challenge, bet, getTeamLogo, currentUsername]);\n  useEffect(() => {\n    const initializeComponent = async () => {\n      setIsLoading(true);\n      setError(null);\n      try {\n        if (!userId) {\n          navigate('/user/login');\n          return;\n        }\n\n        // Fetch all required data\n        await Promise.all([fetchChallenge(), fetchBetDetails(), fetchTeams(), fetchCurrentUsername()]);\n        setIsLoading(false);\n      } catch (error) {\n        console.error('Error initializing component:', error);\n        setError('Failed to load bet details. Please try again.');\n        setIsLoading(false);\n      }\n    };\n    initializeComponent();\n  }, [challengeId, betId, uniqueCode, userId, navigate, fetchChallenge, fetchBetDetails, fetchTeams, fetchCurrentUsername]);\n  useEffect(() => {\n    if (bet && parseInt(user1Id) === parseInt(userId)) {\n      setError(\"You cannot bet against yourself!\");\n      setTimeout(() => {\n        navigate('/user/bets/outgoing');\n      }, 3000);\n    }\n  }, [bet, user1Id, userId, navigate]);\n  useEffect(() => {\n    calculatePotentialReturns();\n  }, [calculatePotentialReturns]);\n  const handleJoinBet = async event => {\n    event.preventDefault();\n    setError('');\n    setSuccess('');\n    try {\n      // Validate user is logged in\n      if (!userId) {\n        throw new Error('Please log in to place a bet');\n      }\n\n      // Validate bet amount\n      if (!betAmount || isNaN(parseFloat(betAmount))) {\n        throw new Error('Please enter a valid bet amount');\n      }\n      const amount = parseFloat(betAmount);\n      let odds;\n      let bet_choice_user2;\n\n      // Set odds and bet choice based on opposite of User 1's choice\n      if (bet.bet_choice_user1 === 'team_a_win') {\n        odds = parseFloat(challenge.odds_team_b);\n        bet_choice_user2 = 'team_b_win';\n      } else if (bet.bet_choice_user1 === 'team_b_win') {\n        odds = parseFloat(challenge.odds_team_a);\n        bet_choice_user2 = 'team_a_win';\n      } else {\n        odds = parseFloat(challenge.odds_draw);\n        bet_choice_user2 = 'draw';\n      }\n\n      // Validate odds calculation\n      if (isNaN(odds)) {\n        throw new Error('Invalid odds calculation');\n      }\n      const requestData = {\n        betId: parseInt(betId),\n        challengeId: parseInt(challenge.challenge_id),\n        userId: parseInt(userId),\n        uniqueCode,\n        amount: amount.toFixed(2),\n        amount_user2: amount.toFixed(2),\n        bet_choice_user2,\n        odds_user2: odds.toFixed(2),\n        potential_return_user2: (amount * odds).toFixed(2),\n        potential_return_win_user2: (amount * odds).toFixed(2),\n        potential_return_draw_user2: (amount * (challenge.odds_draw || 0.9)).toFixed(2),\n        potential_return_loss_user2: (amount * (challenge.odds_lost || 0.2)).toFixed(2),\n        team1_id: parseInt(challenge.team_a_id),\n        team2_id: parseInt(challenge.team_b_id)\n      };\n      console.log('Sending bet data:', requestData);\n      const response = await axios.post(`${API_BASE_URL}/handlers/accept_bet.php`, requestData);\n      console.log('Server response:', response.data);\n      if (response.data.success) {\n        setSuccess('Successfully joined the bet! Redirecting...');\n        setBetAmount('');\n        setTimeout(() => {\n          navigate('/user/bets/accepted', {\n            replace: true\n          });\n        }, 2000);\n      } else {\n        throw new Error(response.data.message || 'Failed to join bet');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error joining bet:', error);\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message || 'An error occurred while joining the bet.');\n    }\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"join-challenge\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading bet details...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 44\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 12\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"join-challenge\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/user/bets'),\n        className: \"back-button\",\n        children: \"Back to Bets\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 7\n    }, this);\n  }\n  if (!challenge || !bet) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"join-challenge\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: \"Bet details not found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/user/bets'),\n        className: \"back-button\",\n        children: \"Back to Bets\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"join-challenge\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Accept Bet\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success-message\",\n      children: success\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 19\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"centered-layout\",\n      children: getTeamDisplay()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"match-details-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"detail-item match-type\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Match Type\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          children: challenge.match_type === 'full_time' ? 'Full Time' : 'Half Time'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"detail-item date\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Match Date\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          children: new Date(challenge.match_date).toLocaleString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"detail-item odds\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Your Odds\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          children: [bet.bet_choice_user1 === 'team_a_win' ? challenge.odds_team_b : challenge.odds_team_a, \"x\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"detail-item amount\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Bet Amount\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          children: [betAmount, \" FanCoins\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"returns-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"return-item win\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Win Return\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          children: [potentialReturns.win, \" FanCoins\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"return-item draw\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Draw Return\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          children: [potentialReturns.draw, \" FanCoins\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"return-item loss\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"label\",\n          children: \"Loss Return\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"value\",\n          children: [potentialReturns.loss, \" FanCoins\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      className: \"bet-form\",\n      onSubmit: handleJoinBet,\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"submit-button\",\n        disabled: !!error,\n        children: \"Accept Bet\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 329,\n    columnNumber: 5\n  }, this);\n};\n_s(JoinChallenge2, \"bpOKAj7xvd1gzm077HkKnWjT94A=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = JoinChallenge2;\nexport default JoinChallenge2;\nvar _c;\n$RefreshReg$(_c, \"JoinChallenge2\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useParams", "useNavigate", "axios", "jsxDEV", "_jsxDEV", "JoinChallenge2", "_s", "challengeId", "betId", "uniqueCode", "navigate", "challenge", "setChallenge", "bet", "setBet", "betAmount", "setBetAmount", "error", "setError", "success", "setSuccess", "currentUsername", "setCurrentUsername", "isLoading", "setIsLoading", "potentialReturns", "setPotentialReturns", "win", "draw", "loss", "teams", "setTeams", "userId", "localStorage", "getItem", "user1Id", "user1_id", "fetchChallenge", "console", "log", "response", "get", "params", "id", "data", "Error", "message", "fetchBetDetails", "amount_user1", "fetchTeams", "status", "warn", "fetchCurrentUsername", "user", "username", "getTeamLogo", "teamName", "team", "find", "t", "name", "logo", "calculatePotentialReturns", "amount", "parseFloat", "isNaN", "winOdds", "bet_choice_user1", "odds_team_b", "odds_team_a", "odds_draw", "drawOdds", "lossOdds", "odds_lost", "toFixed", "getTeamDisplay", "leftTeam", "team_b", "team_a", "leftTeamLogo", "rightTeam", "rightTeamLogo", "className", "children", "src", "alt", "onError", "e", "target", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "user1_name", "initializeComponent", "Promise", "all", "parseInt", "setTimeout", "handleJoinBet", "event", "preventDefault", "odds", "bet_choice_user2", "requestData", "challenge_id", "amount_user2", "odds_user2", "potential_return_user2", "potential_return_win_user2", "potential_return_draw_user2", "potential_return_loss_user2", "team1_id", "team_a_id", "team2_id", "team_b_id", "post", "API_BASE_URL", "replace", "_error$response", "_error$response$data", "onClick", "match_type", "Date", "match_date", "toLocaleString", "onSubmit", "type", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/JoinChallenge2.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport axios from '../utils/axiosConfig';\nimport './JoinChallenge2.css';\nimport '../styles/TeamLogo.css';\n\n// Remove API_BASE_URL since axios is already configured with the correct base URL\n\nconst JoinChallenge2 = () => {\n  const { challengeId, betId, uniqueCode } = useParams();\n  const navigate = useNavigate();\n  const [challenge, setChallenge] = useState(null);\n  const [bet, setBet] = useState(null);\n  const [betAmount, setBetAmount] = useState('');\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [currentUsername, setCurrentUsername] = useState('');\n  const [isLoading, setIsLoading] = useState(true);\n  const [potentialReturns, setPotentialReturns] = useState({\n    win: 0,\n    draw: 0,\n    loss: 0\n  });\n  const [teams, setTeams] = useState([]);\n\n  const userId = localStorage.getItem('userId');\n  const user1Id = bet?.user1_id;\n\n  const fetchChallenge = useCallback(async () => {\n    try {\n      console.log('Fetching challenge with ID:', challengeId);\n      const response = await axios.get(`/handlers/get_challenges.php`, {\n        params: { id: challengeId }\n      });\n      console.log('Challenge response:', response.data);\n      if (response.data.success) {\n        setChallenge(response.data.challenge);\n      } else {\n        throw new Error(response.data.message || 'Failed to fetch challenge');\n      }\n    } catch (error) {\n      console.error('Error fetching challenge:', error);\n      setError('Failed to load challenge details. Please try again.');\n    }\n  }, [challengeId]);\n\n  const fetchBetDetails = useCallback(async () => {\n    try {\n      console.log('Fetching bet details:', { betId, uniqueCode });\n      const response = await axios.get(`/handlers/get_bet_details.php`, {\n        params: { betId, uniqueCode }\n      });\n      console.log('Bet details response:', response.data);\n      if (response.data.success) {\n        setBet(response.data.bet);\n        // Set initial bet amount from user1's amount\n        setBetAmount(response.data.bet.amount_user1);\n      } else {\n        throw new Error(response.data.message || 'Failed to fetch bet details');\n      }\n    } catch (error) {\n      console.error('Error fetching bet details:', error);\n      setError('Error loading bet details. Please try again.');\n    }\n  }, [betId, uniqueCode]);\n\n  const fetchTeams = useCallback(async () => {\n    try {\n      const response = await axios.get(`/handlers/team_management.php`);\n      if (response.data.status === 200) {\n        setTeams(response.data.data);\n      } else {\n        console.warn('Team data not in expected format:', response.data);\n      }\n    } catch (error) {\n      console.error('Error fetching teams:', error);\n    }\n  }, []);\n\n  const fetchCurrentUsername = useCallback(async () => {\n    try {\n      if (!userId) {\n        throw new Error('User ID not found');\n      }\n      const response = await axios.get(`/handlers/user_data.php`, {\n        params: { userId }\n      });\n      if (response.data.success) {\n        setCurrentUsername(response.data.user.username);\n      } else {\n        throw new Error(response.data.message || 'Failed to fetch user data');\n      }\n    } catch (error) {\n      console.error('Error fetching current username:', error);\n      setError('Error loading user data');\n    }\n  }, [userId]);\n\n  const getTeamLogo = useCallback((teamName) => {\n    const team = teams.find(t => t.name === teamName);\n    return team ? `/${team.logo}` : '/default-team-logo.png';\n  }, [teams]);\n\n  const calculatePotentialReturns = useCallback(() => {\n    if (!betAmount || !challenge || !bet) return;\n    \n    try {\n      const amount = parseFloat(betAmount);\n      if (isNaN(amount)) return;\n\n      let winOdds;\n      // Determine odds based on user1's bet choice\n      if (bet.bet_choice_user1 === 'team_a_win') {\n        winOdds = parseFloat(challenge.odds_team_b);\n      } else if (bet.bet_choice_user1 === 'team_b_win') {\n        winOdds = parseFloat(challenge.odds_team_a);\n      } else {\n        winOdds = parseFloat(challenge.odds_draw);\n      }\n\n      const drawOdds = parseFloat(challenge.odds_draw) || 0.9;\n      const lossOdds = parseFloat(challenge.odds_lost) || 0.2;\n\n      setPotentialReturns({\n        win: (amount * winOdds).toFixed(2),\n        draw: (amount * drawOdds).toFixed(2),\n        loss: (amount * lossOdds).toFixed(2)\n      });\n    } catch (error) {\n      console.error('Error calculating returns:', error);\n    }\n  }, [betAmount, challenge, bet]);\n\n  const getTeamDisplay = useCallback(() => {\n    if (!challenge || !bet) return null;\n\n    // Determine teams based on user1's bet choice\n    const leftTeam = bet.bet_choice_user1 === 'team_b_win' ? challenge.team_b : challenge.team_a;\n    const leftTeamLogo = bet.bet_choice_user1 === 'team_b_win' ? getTeamLogo(challenge.team_b) : getTeamLogo(challenge.team_a);\n    \n    const rightTeam = bet.bet_choice_user1 === 'team_b_win' ? challenge.team_a : challenge.team_b;\n    const rightTeamLogo = bet.bet_choice_user1 === 'team_b_win' ? getTeamLogo(challenge.team_a) : getTeamLogo(challenge.team_b);\n\n    return (\n      <div className=\"challenge-details\">\n        <div className=\"team-info\">\n          <div className=\"challenge-team-wrapper\">\n            <img \n              src={leftTeamLogo}\n              alt={leftTeam}\n              className=\"challenge-team-logo\"\n              onError={(e) => {\n                e.target.src = '/default-team-logo.png';\n              }}\n            />\n            <span className=\"challenge-team-name\">{leftTeam}</span>\n            <span className=\"player-name\">{bet.user1_name}</span>\n            <span className=\"opponent-pick\">Opponent's Pick</span>\n          </div>\n        </div>\n\n        <div className=\"vs-container\">\n          <span className=\"vs\">VS</span>\n        </div>\n\n        <div className=\"team-info\">\n          <div className=\"challenge-team-wrapper\">\n            <img \n              src={rightTeamLogo}\n              alt={rightTeam}\n              className=\"challenge-team-logo\"\n              onError={(e) => {\n                e.target.src = '/default-team-logo.png';\n              }}\n            />\n            <span className=\"challenge-team-name\">{rightTeam}</span>\n            <span className=\"player-name\">{currentUsername}</span>\n          </div>\n        </div>\n      </div>\n    );\n  }, [challenge, bet, getTeamLogo, currentUsername]);\n\n  useEffect(() => {\n    const initializeComponent = async () => {\n      setIsLoading(true);\n      setError(null);\n\n      try {\n        if (!userId) {\n          navigate('/user/login');\n          return;\n        }\n\n        // Fetch all required data\n        await Promise.all([\n          fetchChallenge(),\n          fetchBetDetails(),\n          fetchTeams(),\n          fetchCurrentUsername()\n        ]);\n\n        setIsLoading(false);\n      } catch (error) {\n        console.error('Error initializing component:', error);\n        setError('Failed to load bet details. Please try again.');\n        setIsLoading(false);\n      }\n    };\n\n    initializeComponent();\n  }, [challengeId, betId, uniqueCode, userId, navigate, fetchChallenge, fetchBetDetails, fetchTeams, fetchCurrentUsername]);\n\n  useEffect(() => {\n    if (bet && parseInt(user1Id) === parseInt(userId)) {\n      setError(\"You cannot bet against yourself!\");\n      setTimeout(() => {\n        navigate('/user/bets/outgoing');\n      }, 3000);\n    }\n  }, [bet, user1Id, userId, navigate]);\n\n  useEffect(() => {\n    calculatePotentialReturns();\n  }, [calculatePotentialReturns]);\n\n  const handleJoinBet = async (event) => {\n    event.preventDefault();\n    setError('');\n    setSuccess('');\n\n    try {\n      // Validate user is logged in\n      if (!userId) {\n        throw new Error('Please log in to place a bet');\n      }\n\n      // Validate bet amount\n      if (!betAmount || isNaN(parseFloat(betAmount))) {\n        throw new Error('Please enter a valid bet amount');\n      }\n\n      const amount = parseFloat(betAmount);\n      let odds;\n      let bet_choice_user2;\n      \n      // Set odds and bet choice based on opposite of User 1's choice\n      if (bet.bet_choice_user1 === 'team_a_win') {\n        odds = parseFloat(challenge.odds_team_b);\n        bet_choice_user2 = 'team_b_win';\n      } else if (bet.bet_choice_user1 === 'team_b_win') {\n        odds = parseFloat(challenge.odds_team_a);\n        bet_choice_user2 = 'team_a_win';\n      } else {\n        odds = parseFloat(challenge.odds_draw);\n        bet_choice_user2 = 'draw';\n      }\n\n      // Validate odds calculation\n      if (isNaN(odds)) {\n        throw new Error('Invalid odds calculation');\n      }\n\n      const requestData = {\n        betId: parseInt(betId),\n        challengeId: parseInt(challenge.challenge_id),\n        userId: parseInt(userId),\n        uniqueCode,\n        amount: amount.toFixed(2),\n        amount_user2: amount.toFixed(2),\n        bet_choice_user2,\n        odds_user2: odds.toFixed(2),\n        potential_return_user2: (amount * odds).toFixed(2),\n        potential_return_win_user2: (amount * odds).toFixed(2),\n        potential_return_draw_user2: (amount * (challenge.odds_draw || 0.9)).toFixed(2),\n        potential_return_loss_user2: (amount * (challenge.odds_lost || 0.2)).toFixed(2),\n        team1_id: parseInt(challenge.team_a_id),\n        team2_id: parseInt(challenge.team_b_id)\n      };\n\n      console.log('Sending bet data:', requestData);\n\n      const response = await axios.post(`${API_BASE_URL}/handlers/accept_bet.php`, requestData);\n      console.log('Server response:', response.data);\n\n      if (response.data.success) {\n        setSuccess('Successfully joined the bet! Redirecting...');\n        setBetAmount('');\n        \n        setTimeout(() => {\n          navigate('/user/bets/accepted', { replace: true });\n        }, 2000);\n      } else {\n        throw new Error(response.data.message || 'Failed to join bet');\n      }\n    } catch (error) {\n      console.error('Error joining bet:', error);\n      setError(error.response?.data?.message || error.message || 'An error occurred while joining the bet.');\n    }\n  };\n\n  if (isLoading) {\n    return <div className=\"join-challenge\"><p>Loading bet details...</p></div>;\n  }\n\n  if (error) {\n    return (\n      <div className=\"join-challenge\">\n        <div className=\"error-message\">{error}</div>\n        <button onClick={() => navigate('/user/bets')} className=\"back-button\">\n          Back to Bets\n        </button>\n      </div>\n    );\n  }\n\n  if (!challenge || !bet) {\n    return (\n      <div className=\"join-challenge\">\n        <div className=\"error-message\">Bet details not found</div>\n        <button onClick={() => navigate('/user/bets')} className=\"back-button\">\n          Back to Bets\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"join-challenge\">\n      <h1>Accept Bet</h1>\n      {error && <div className=\"error-message\">{error}</div>}\n      {success && <div className=\"success-message\">{success}</div>}\n      \n      <div className=\"centered-layout\">\n        {getTeamDisplay()}\n      </div>\n\n      <div className=\"match-details-grid\">\n        <div className=\"detail-item match-type\">\n          <span className=\"label\">Match Type</span>\n          <span className=\"value\">{challenge.match_type === 'full_time' ? 'Full Time' : 'Half Time'}</span>\n        </div>\n\n        <div className=\"detail-item date\">\n          <span className=\"label\">Match Date</span>\n          <span className=\"value\">{new Date(challenge.match_date).toLocaleString()}</span>\n        </div>\n\n        <div className=\"detail-item odds\">\n          <span className=\"label\">Your Odds</span>\n          <span className=\"value\">{bet.bet_choice_user1 === 'team_a_win' ? challenge.odds_team_b : challenge.odds_team_a}x</span>\n        </div>\n\n        <div className=\"detail-item amount\">\n          <span className=\"label\">Bet Amount</span>\n          <span className=\"value\">{betAmount} FanCoins</span>\n        </div>\n      </div>\n\n      <div className=\"returns-grid\">\n        <div className=\"return-item win\">\n          <span className=\"label\">Win Return</span>\n          <span className=\"value\">{potentialReturns.win} FanCoins</span>\n        </div>\n        <div className=\"return-item draw\">\n          <span className=\"label\">Draw Return</span>\n          <span className=\"value\">{potentialReturns.draw} FanCoins</span>\n        </div>\n        <div className=\"return-item loss\">\n          <span className=\"label\">Loss Return</span>\n          <span className=\"value\">{potentialReturns.loss} FanCoins</span>\n        </div>\n      </div>\n\n      <form className=\"bet-form\" onSubmit={handleJoinBet}>\n        <button type=\"submit\" className=\"submit-button\" disabled={!!error}>\n          Accept Bet\n        </button>\n      </form>\n    </div>\n  );\n};\n\nexport default JoinChallenge2;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAO,sBAAsB;AAC7B,OAAO,wBAAwB;;AAE/B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC,WAAW;IAAEC,KAAK;IAAEC;EAAW,CAAC,GAAGT,SAAS,CAAC,CAAC;EACtD,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACgB,GAAG,EAAEC,MAAM,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACpC,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC4B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7B,QAAQ,CAAC;IACvD8B,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMmC,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;EAC7C,MAAMC,OAAO,GAAGtB,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEuB,QAAQ;EAE7B,MAAMC,cAAc,GAAGtC,WAAW,CAAC,YAAY;IAC7C,IAAI;MACFuC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEhC,WAAW,CAAC;MACvD,MAAMiC,QAAQ,GAAG,MAAMtC,KAAK,CAACuC,GAAG,CAAC,8BAA8B,EAAE;QAC/DC,MAAM,EAAE;UAAEC,EAAE,EAAEpC;QAAY;MAC5B,CAAC,CAAC;MACF+B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEC,QAAQ,CAACI,IAAI,CAAC;MACjD,IAAIJ,QAAQ,CAACI,IAAI,CAACzB,OAAO,EAAE;QACzBP,YAAY,CAAC4B,QAAQ,CAACI,IAAI,CAACjC,SAAS,CAAC;MACvC,CAAC,MAAM;QACL,MAAM,IAAIkC,KAAK,CAACL,QAAQ,CAACI,IAAI,CAACE,OAAO,IAAI,2BAA2B,CAAC;MACvE;IACF,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDC,QAAQ,CAAC,qDAAqD,CAAC;IACjE;EACF,CAAC,EAAE,CAACX,WAAW,CAAC,CAAC;EAEjB,MAAMwC,eAAe,GAAGhD,WAAW,CAAC,YAAY;IAC9C,IAAI;MACFuC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;QAAE/B,KAAK;QAAEC;MAAW,CAAC,CAAC;MAC3D,MAAM+B,QAAQ,GAAG,MAAMtC,KAAK,CAACuC,GAAG,CAAC,+BAA+B,EAAE;QAChEC,MAAM,EAAE;UAAElC,KAAK;UAAEC;QAAW;MAC9B,CAAC,CAAC;MACF6B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,QAAQ,CAACI,IAAI,CAAC;MACnD,IAAIJ,QAAQ,CAACI,IAAI,CAACzB,OAAO,EAAE;QACzBL,MAAM,CAAC0B,QAAQ,CAACI,IAAI,CAAC/B,GAAG,CAAC;QACzB;QACAG,YAAY,CAACwB,QAAQ,CAACI,IAAI,CAAC/B,GAAG,CAACmC,YAAY,CAAC;MAC9C,CAAC,MAAM;QACL,MAAM,IAAIH,KAAK,CAACL,QAAQ,CAACI,IAAI,CAACE,OAAO,IAAI,6BAA6B,CAAC;MACzE;IACF,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDC,QAAQ,CAAC,8CAA8C,CAAC;IAC1D;EACF,CAAC,EAAE,CAACV,KAAK,EAAEC,UAAU,CAAC,CAAC;EAEvB,MAAMwC,UAAU,GAAGlD,WAAW,CAAC,YAAY;IACzC,IAAI;MACF,MAAMyC,QAAQ,GAAG,MAAMtC,KAAK,CAACuC,GAAG,CAAC,+BAA+B,CAAC;MACjE,IAAID,QAAQ,CAACI,IAAI,CAACM,MAAM,KAAK,GAAG,EAAE;QAChCnB,QAAQ,CAACS,QAAQ,CAACI,IAAI,CAACA,IAAI,CAAC;MAC9B,CAAC,MAAM;QACLN,OAAO,CAACa,IAAI,CAAC,mCAAmC,EAAEX,QAAQ,CAACI,IAAI,CAAC;MAClE;IACF,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmC,oBAAoB,GAAGrD,WAAW,CAAC,YAAY;IACnD,IAAI;MACF,IAAI,CAACiC,MAAM,EAAE;QACX,MAAM,IAAIa,KAAK,CAAC,mBAAmB,CAAC;MACtC;MACA,MAAML,QAAQ,GAAG,MAAMtC,KAAK,CAACuC,GAAG,CAAC,yBAAyB,EAAE;QAC1DC,MAAM,EAAE;UAAEV;QAAO;MACnB,CAAC,CAAC;MACF,IAAIQ,QAAQ,CAACI,IAAI,CAACzB,OAAO,EAAE;QACzBG,kBAAkB,CAACkB,QAAQ,CAACI,IAAI,CAACS,IAAI,CAACC,QAAQ,CAAC;MACjD,CAAC,MAAM;QACL,MAAM,IAAIT,KAAK,CAACL,QAAQ,CAACI,IAAI,CAACE,OAAO,IAAI,2BAA2B,CAAC;MACvE;IACF,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDC,QAAQ,CAAC,yBAAyB,CAAC;IACrC;EACF,CAAC,EAAE,CAACc,MAAM,CAAC,CAAC;EAEZ,MAAMuB,WAAW,GAAGxD,WAAW,CAAEyD,QAAQ,IAAK;IAC5C,MAAMC,IAAI,GAAG3B,KAAK,CAAC4B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKJ,QAAQ,CAAC;IACjD,OAAOC,IAAI,GAAG,IAAIA,IAAI,CAACI,IAAI,EAAE,GAAG,wBAAwB;EAC1D,CAAC,EAAE,CAAC/B,KAAK,CAAC,CAAC;EAEX,MAAMgC,yBAAyB,GAAG/D,WAAW,CAAC,MAAM;IAClD,IAAI,CAACgB,SAAS,IAAI,CAACJ,SAAS,IAAI,CAACE,GAAG,EAAE;IAEtC,IAAI;MACF,MAAMkD,MAAM,GAAGC,UAAU,CAACjD,SAAS,CAAC;MACpC,IAAIkD,KAAK,CAACF,MAAM,CAAC,EAAE;MAEnB,IAAIG,OAAO;MACX;MACA,IAAIrD,GAAG,CAACsD,gBAAgB,KAAK,YAAY,EAAE;QACzCD,OAAO,GAAGF,UAAU,CAACrD,SAAS,CAACyD,WAAW,CAAC;MAC7C,CAAC,MAAM,IAAIvD,GAAG,CAACsD,gBAAgB,KAAK,YAAY,EAAE;QAChDD,OAAO,GAAGF,UAAU,CAACrD,SAAS,CAAC0D,WAAW,CAAC;MAC7C,CAAC,MAAM;QACLH,OAAO,GAAGF,UAAU,CAACrD,SAAS,CAAC2D,SAAS,CAAC;MAC3C;MAEA,MAAMC,QAAQ,GAAGP,UAAU,CAACrD,SAAS,CAAC2D,SAAS,CAAC,IAAI,GAAG;MACvD,MAAME,QAAQ,GAAGR,UAAU,CAACrD,SAAS,CAAC8D,SAAS,CAAC,IAAI,GAAG;MAEvD/C,mBAAmB,CAAC;QAClBC,GAAG,EAAE,CAACoC,MAAM,GAAGG,OAAO,EAAEQ,OAAO,CAAC,CAAC,CAAC;QAClC9C,IAAI,EAAE,CAACmC,MAAM,GAAGQ,QAAQ,EAAEG,OAAO,CAAC,CAAC,CAAC;QACpC7C,IAAI,EAAE,CAACkC,MAAM,GAAGS,QAAQ,EAAEE,OAAO,CAAC,CAAC;MACrC,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOzD,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC,EAAE,CAACF,SAAS,EAAEJ,SAAS,EAAEE,GAAG,CAAC,CAAC;EAE/B,MAAM8D,cAAc,GAAG5E,WAAW,CAAC,MAAM;IACvC,IAAI,CAACY,SAAS,IAAI,CAACE,GAAG,EAAE,OAAO,IAAI;;IAEnC;IACA,MAAM+D,QAAQ,GAAG/D,GAAG,CAACsD,gBAAgB,KAAK,YAAY,GAAGxD,SAAS,CAACkE,MAAM,GAAGlE,SAAS,CAACmE,MAAM;IAC5F,MAAMC,YAAY,GAAGlE,GAAG,CAACsD,gBAAgB,KAAK,YAAY,GAAGZ,WAAW,CAAC5C,SAAS,CAACkE,MAAM,CAAC,GAAGtB,WAAW,CAAC5C,SAAS,CAACmE,MAAM,CAAC;IAE1H,MAAME,SAAS,GAAGnE,GAAG,CAACsD,gBAAgB,KAAK,YAAY,GAAGxD,SAAS,CAACmE,MAAM,GAAGnE,SAAS,CAACkE,MAAM;IAC7F,MAAMI,aAAa,GAAGpE,GAAG,CAACsD,gBAAgB,KAAK,YAAY,GAAGZ,WAAW,CAAC5C,SAAS,CAACmE,MAAM,CAAC,GAAGvB,WAAW,CAAC5C,SAAS,CAACkE,MAAM,CAAC;IAE3H,oBACEzE,OAAA;MAAK8E,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC/E,OAAA;QAAK8E,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB/E,OAAA;UAAK8E,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC/E,OAAA;YACEgF,GAAG,EAAEL,YAAa;YAClBM,GAAG,EAAET,QAAS;YACdM,SAAS,EAAC,qBAAqB;YAC/BI,OAAO,EAAGC,CAAC,IAAK;cACdA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG,wBAAwB;YACzC;UAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFxF,OAAA;YAAM8E,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAEP;UAAQ;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvDxF,OAAA;YAAM8E,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEtE,GAAG,CAACgF;UAAU;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrDxF,OAAA;YAAM8E,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAe;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxF,OAAA;QAAK8E,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3B/E,OAAA;UAAM8E,SAAS,EAAC,IAAI;UAAAC,QAAA,EAAC;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eAENxF,OAAA;QAAK8E,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB/E,OAAA;UAAK8E,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC/E,OAAA;YACEgF,GAAG,EAAEH,aAAc;YACnBI,GAAG,EAAEL,SAAU;YACfE,SAAS,EAAC,qBAAqB;YAC/BI,OAAO,EAAGC,CAAC,IAAK;cACdA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG,wBAAwB;YACzC;UAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFxF,OAAA;YAAM8E,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAEH;UAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxDxF,OAAA;YAAM8E,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE9D;UAAe;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC,EAAE,CAACjF,SAAS,EAAEE,GAAG,EAAE0C,WAAW,EAAElC,eAAe,CAAC,CAAC;EAElDvB,SAAS,CAAC,MAAM;IACd,MAAMgG,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACtCtE,YAAY,CAAC,IAAI,CAAC;MAClBN,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACF,IAAI,CAACc,MAAM,EAAE;UACXtB,QAAQ,CAAC,aAAa,CAAC;UACvB;QACF;;QAEA;QACA,MAAMqF,OAAO,CAACC,GAAG,CAAC,CAChB3D,cAAc,CAAC,CAAC,EAChBU,eAAe,CAAC,CAAC,EACjBE,UAAU,CAAC,CAAC,EACZG,oBAAoB,CAAC,CAAC,CACvB,CAAC;QAEF5B,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,CAAC,OAAOP,KAAK,EAAE;QACdqB,OAAO,CAACrB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDC,QAAQ,CAAC,+CAA+C,CAAC;QACzDM,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDsE,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACvF,WAAW,EAAEC,KAAK,EAAEC,UAAU,EAAEuB,MAAM,EAAEtB,QAAQ,EAAE2B,cAAc,EAAEU,eAAe,EAAEE,UAAU,EAAEG,oBAAoB,CAAC,CAAC;EAEzHtD,SAAS,CAAC,MAAM;IACd,IAAIe,GAAG,IAAIoF,QAAQ,CAAC9D,OAAO,CAAC,KAAK8D,QAAQ,CAACjE,MAAM,CAAC,EAAE;MACjDd,QAAQ,CAAC,kCAAkC,CAAC;MAC5CgF,UAAU,CAAC,MAAM;QACfxF,QAAQ,CAAC,qBAAqB,CAAC;MACjC,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAE,CAACG,GAAG,EAAEsB,OAAO,EAAEH,MAAM,EAAEtB,QAAQ,CAAC,CAAC;EAEpCZ,SAAS,CAAC,MAAM;IACdgE,yBAAyB,CAAC,CAAC;EAC7B,CAAC,EAAE,CAACA,yBAAyB,CAAC,CAAC;EAE/B,MAAMqC,aAAa,GAAG,MAAOC,KAAK,IAAK;IACrCA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBnF,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF;MACA,IAAI,CAACY,MAAM,EAAE;QACX,MAAM,IAAIa,KAAK,CAAC,8BAA8B,CAAC;MACjD;;MAEA;MACA,IAAI,CAAC9B,SAAS,IAAIkD,KAAK,CAACD,UAAU,CAACjD,SAAS,CAAC,CAAC,EAAE;QAC9C,MAAM,IAAI8B,KAAK,CAAC,iCAAiC,CAAC;MACpD;MAEA,MAAMkB,MAAM,GAAGC,UAAU,CAACjD,SAAS,CAAC;MACpC,IAAIuF,IAAI;MACR,IAAIC,gBAAgB;;MAEpB;MACA,IAAI1F,GAAG,CAACsD,gBAAgB,KAAK,YAAY,EAAE;QACzCmC,IAAI,GAAGtC,UAAU,CAACrD,SAAS,CAACyD,WAAW,CAAC;QACxCmC,gBAAgB,GAAG,YAAY;MACjC,CAAC,MAAM,IAAI1F,GAAG,CAACsD,gBAAgB,KAAK,YAAY,EAAE;QAChDmC,IAAI,GAAGtC,UAAU,CAACrD,SAAS,CAAC0D,WAAW,CAAC;QACxCkC,gBAAgB,GAAG,YAAY;MACjC,CAAC,MAAM;QACLD,IAAI,GAAGtC,UAAU,CAACrD,SAAS,CAAC2D,SAAS,CAAC;QACtCiC,gBAAgB,GAAG,MAAM;MAC3B;;MAEA;MACA,IAAItC,KAAK,CAACqC,IAAI,CAAC,EAAE;QACf,MAAM,IAAIzD,KAAK,CAAC,0BAA0B,CAAC;MAC7C;MAEA,MAAM2D,WAAW,GAAG;QAClBhG,KAAK,EAAEyF,QAAQ,CAACzF,KAAK,CAAC;QACtBD,WAAW,EAAE0F,QAAQ,CAACtF,SAAS,CAAC8F,YAAY,CAAC;QAC7CzE,MAAM,EAAEiE,QAAQ,CAACjE,MAAM,CAAC;QACxBvB,UAAU;QACVsD,MAAM,EAAEA,MAAM,CAACW,OAAO,CAAC,CAAC,CAAC;QACzBgC,YAAY,EAAE3C,MAAM,CAACW,OAAO,CAAC,CAAC,CAAC;QAC/B6B,gBAAgB;QAChBI,UAAU,EAAEL,IAAI,CAAC5B,OAAO,CAAC,CAAC,CAAC;QAC3BkC,sBAAsB,EAAE,CAAC7C,MAAM,GAAGuC,IAAI,EAAE5B,OAAO,CAAC,CAAC,CAAC;QAClDmC,0BAA0B,EAAE,CAAC9C,MAAM,GAAGuC,IAAI,EAAE5B,OAAO,CAAC,CAAC,CAAC;QACtDoC,2BAA2B,EAAE,CAAC/C,MAAM,IAAIpD,SAAS,CAAC2D,SAAS,IAAI,GAAG,CAAC,EAAEI,OAAO,CAAC,CAAC,CAAC;QAC/EqC,2BAA2B,EAAE,CAAChD,MAAM,IAAIpD,SAAS,CAAC8D,SAAS,IAAI,GAAG,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC;QAC/EsC,QAAQ,EAAEf,QAAQ,CAACtF,SAAS,CAACsG,SAAS,CAAC;QACvCC,QAAQ,EAAEjB,QAAQ,CAACtF,SAAS,CAACwG,SAAS;MACxC,CAAC;MAED7E,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEiE,WAAW,CAAC;MAE7C,MAAMhE,QAAQ,GAAG,MAAMtC,KAAK,CAACkH,IAAI,CAAC,GAAGC,YAAY,0BAA0B,EAAEb,WAAW,CAAC;MACzFlE,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,QAAQ,CAACI,IAAI,CAAC;MAE9C,IAAIJ,QAAQ,CAACI,IAAI,CAACzB,OAAO,EAAE;QACzBC,UAAU,CAAC,6CAA6C,CAAC;QACzDJ,YAAY,CAAC,EAAE,CAAC;QAEhBkF,UAAU,CAAC,MAAM;UACfxF,QAAQ,CAAC,qBAAqB,EAAE;YAAE4G,OAAO,EAAE;UAAK,CAAC,CAAC;QACpD,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAM,IAAIzE,KAAK,CAACL,QAAQ,CAACI,IAAI,CAACE,OAAO,IAAI,oBAAoB,CAAC;MAChE;IACF,CAAC,CAAC,OAAO7B,KAAK,EAAE;MAAA,IAAAsG,eAAA,EAAAC,oBAAA;MACdlF,OAAO,CAACrB,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CC,QAAQ,CAAC,EAAAqG,eAAA,GAAAtG,KAAK,CAACuB,QAAQ,cAAA+E,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB3E,IAAI,cAAA4E,oBAAA,uBAApBA,oBAAA,CAAsB1E,OAAO,KAAI7B,KAAK,CAAC6B,OAAO,IAAI,0CAA0C,CAAC;IACxG;EACF,CAAC;EAED,IAAIvB,SAAS,EAAE;IACb,oBAAOnB,OAAA;MAAK8E,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAAC/E,OAAA;QAAA+E,QAAA,EAAG;MAAsB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC5E;EAEA,IAAI3E,KAAK,EAAE;IACT,oBACEb,OAAA;MAAK8E,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B/E,OAAA;QAAK8E,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAElE;MAAK;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5CxF,OAAA;QAAQqH,OAAO,EAAEA,CAAA,KAAM/G,QAAQ,CAAC,YAAY,CAAE;QAACwE,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAEvE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,IAAI,CAACjF,SAAS,IAAI,CAACE,GAAG,EAAE;IACtB,oBACET,OAAA;MAAK8E,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B/E,OAAA;QAAK8E,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAqB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC1DxF,OAAA;QAAQqH,OAAO,EAAEA,CAAA,KAAM/G,QAAQ,CAAC,YAAY,CAAE;QAACwE,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAEvE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACExF,OAAA;IAAK8E,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B/E,OAAA;MAAA+E,QAAA,EAAI;IAAU;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAClB3E,KAAK,iBAAIb,OAAA;MAAK8E,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAElE;IAAK;MAAAwE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACrDzE,OAAO,iBAAIf,OAAA;MAAK8E,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAAEhE;IAAO;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAE5DxF,OAAA;MAAK8E,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC7BR,cAAc,CAAC;IAAC;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eAENxF,OAAA;MAAK8E,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjC/E,OAAA;QAAK8E,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrC/E,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAC;QAAU;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzCxF,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAExE,SAAS,CAAC+G,UAAU,KAAK,WAAW,GAAG,WAAW,GAAG;QAAW;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9F,CAAC,eAENxF,OAAA;QAAK8E,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B/E,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAC;QAAU;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzCxF,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE,IAAIwC,IAAI,CAAChH,SAAS,CAACiH,UAAU,CAAC,CAACC,cAAc,CAAC;QAAC;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E,CAAC,eAENxF,OAAA;QAAK8E,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B/E,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAC;QAAS;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxCxF,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,GAAEtE,GAAG,CAACsD,gBAAgB,KAAK,YAAY,GAAGxD,SAAS,CAACyD,WAAW,GAAGzD,SAAS,CAAC0D,WAAW,EAAC,GAAC;QAAA;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpH,CAAC,eAENxF,OAAA;QAAK8E,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC/E,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAC;QAAU;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzCxF,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,GAAEpE,SAAS,EAAC,WAAS;QAAA;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENxF,OAAA;MAAK8E,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B/E,OAAA;QAAK8E,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B/E,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAC;QAAU;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzCxF,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,GAAE1D,gBAAgB,CAACE,GAAG,EAAC,WAAS;QAAA;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACNxF,OAAA;QAAK8E,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B/E,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAC;QAAW;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1CxF,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,GAAE1D,gBAAgB,CAACG,IAAI,EAAC,WAAS;QAAA;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACNxF,OAAA;QAAK8E,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B/E,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAC;QAAW;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1CxF,OAAA;UAAM8E,SAAS,EAAC,OAAO;UAAAC,QAAA,GAAE1D,gBAAgB,CAACI,IAAI,EAAC,WAAS;QAAA;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENxF,OAAA;MAAM8E,SAAS,EAAC,UAAU;MAAC4C,QAAQ,EAAE3B,aAAc;MAAAhB,QAAA,eACjD/E,OAAA;QAAQ2H,IAAI,EAAC,QAAQ;QAAC7C,SAAS,EAAC,eAAe;QAAC8C,QAAQ,EAAE,CAAC,CAAC/G,KAAM;QAAAkE,QAAA,EAAC;MAEnE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACtF,EAAA,CArXID,cAAc;EAAA,QACyBL,SAAS,EACnCC,WAAW;AAAA;AAAAgI,EAAA,GAFxB5H,cAAc;AAuXpB,eAAeA,cAAc;AAAC,IAAA4H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}