<?php
require_once 'backend/includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    echo "Current user_otp table structure:\n";
    $stmt = $conn->prepare('SHOW CREATE TABLE user_otp');
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo $result['Create Table'] . "\n\n";
    
    echo "Testing INSERT with current structure:\n";
    
    // Test insert with correct column name
    $userId = 1;
    $otp = '123456';
    $expiresAt = date('Y-m-d H:i:s', time() + 300);
    
    // First, let's see what columns exist
    $stmt = $conn->prepare('DESCRIBE user_otp');
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Available columns:\n";
    foreach ($columns as $col) {
        echo "- {$col['Field']}\n";
    }
    
    // Try to insert with the correct column name
    if (in_array('expiry', array_column($columns, 'Field'))) {
        echo "\nUsing 'expiry' column:\n";
        $stmt = $conn->prepare("INSERT INTO user_otp (user_id, otp, expiry) VALUES (?, ?, ?)");
        $stmt->execute([$userId, $otp, $expiresAt]);
        echo "✅ Insert successful\n";
    } elseif (in_array('expires_at', array_column($columns, 'Field'))) {
        echo "\nUsing 'expires_at' column:\n";
        $stmt = $conn->prepare("INSERT INTO user_otp (user_id, otp, expires_at) VALUES (?, ?, ?)");
        $stmt->execute([$userId, $otp, $expiresAt]);
        echo "✅ Insert successful\n";
    }
    
    // Check if the record was inserted
    $stmt = $conn->prepare("SELECT * FROM user_otp WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
    $stmt->execute([$userId]);
    $record = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($record) {
        echo "✅ Record found:\n";
        foreach ($record as $key => $value) {
            echo "  {$key}: {$value}\n";
        }
    } else {
        echo "❌ No record found\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
