<?php
require_once 'backend/includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    echo "users table structure:\n";
    $stmt = $conn->prepare('DESCRIBE users');
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $col) {
        echo "- {$col['Field']} ({$col['Type']})\n";
    }
    
    echo "\nSample user data:\n";
    $stmt = $conn->prepare('SELECT user_id, username, email, password_hash, otp_enabled, tfa_enabled, auth_method FROM users WHERE username = "demohomexx" LIMIT 1');
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "Username: {$user['username']}\n";
        echo "Email: {$user['email']}\n";
        echo "Password Hash: " . substr($user['password_hash'], 0, 20) . "...\n";
        echo "OTP Enabled: " . ($user['otp_enabled'] ? 'Yes' : 'No') . "\n";
        echo "2FA Enabled: " . ($user['tfa_enabled'] ? 'Yes' : 'No') . "\n";
        echo "Auth Method: {$user['auth_method']}\n";
    } else {
        echo "User not found\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
