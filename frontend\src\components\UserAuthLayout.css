/* Full-Width UserAuthLayout Component Styles */

:root {
    /* Primary Colors */
    --primary-green: #2C5F2D;
    --secondary-green: #52B788;
    --light-green: #A7D129;

    /* Neutral Colors */
    --white: #FFFFFF;
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;

    /* Semantic Colors */
    --success: #10B981;
    --warning: #F59E0B;
    --error: #EF4444;
    --info: #3B82F6;

    /* Shadows */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Full-Width Page Layout */
.fullwidth-auth-page {
    min-height: 100vh;
    height: 100vh;
    width: 100%;
    display: flex;
    flex-direction: column;
    background: var(--white);
    overflow: visible;
}

/* Header */
.fullwidth-auth-header {
    width: 100%;
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: 1rem 0;
}

.fullwidth-auth-header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.fullwidth-auth-logo {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-green);
    text-decoration: none;
    transition: var(--transition-normal);
}

.fullwidth-auth-logo:hover {
    color: var(--secondary-green);
}

/* Main Content with Background */
.fullwidth-auth-main {
    flex: 1;
    width: 100%;
    display: flex;
    align-items: flex-start; /* Changed to flex-start for better scrolling */
    justify-content: center;
    padding: 2rem 1.5rem;
    background: linear-gradient(135deg, rgba(44, 95, 45, 0.1) 0%, rgba(82, 183, 136, 0.1) 100%),
                url('https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80') center/cover;
    background-attachment: scroll; /* Changed to scroll to fix scrolling issues */
    position: relative;
    height: 100%; /* Allow it to fill parent and scroll naturally */
    overflow-y: auto; /* Enable vertical scrolling */
}

.fullwidth-auth-main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    z-index: 1;
}

.fullwidth-auth-content {
    width: 100%;
    max-width: 1200px;
    display: flex;
    justify-content: center;
    position: relative;
    z-index: 2;
    flex-shrink: 0; /* Prevent content from shrinking */
}

.fullwidth-auth-form-section {
    width: 100%;
    max-width: 480px;
    background: var(--white);
    padding: 3rem 2rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    position: relative;
    z-index: 3;
}

.fullwidth-auth-form-container {
    width: 100%;
}

/* Title Section */
.fullwidth-auth-title-section {
    text-align: center;
    margin-bottom: 2rem;
}

.fullwidth-auth-title {
    font-size: 1.875rem;
    font-weight: 500;
    color: var(--gray-900);
    margin: 0 0 0.5rem 0;
    line-height: 1.3;
}

.fullwidth-auth-subtitle {
    font-size: 1rem;
    font-weight: 400;
    color: var(--gray-600);
    margin: 0;
    line-height: 1.5;
}

/* Form Content Area */
.fullwidth-auth-form-content {
    width: 100%;
}

/* Footer */
.fullwidth-auth-footer {
    width: 100%;
    background: var(--white);
    border-top: 1px solid var(--gray-200);
    padding: 1rem 0;
    margin-top: auto;
}

.fullwidth-auth-footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
    text-align: center;
}

.fullwidth-auth-footer p {
    font-size: 0.875rem;
    color: var(--gray-500);
    margin: 0;
}

/* Registration Page Variant */
.fullwidth-auth-page.registration .fullwidth-auth-main {
    background:
        linear-gradient(135deg, rgba(82, 183, 136, 0.1) 0%, rgba(167, 209, 41, 0.1) 100%),
        url('https://images.unsplash.com/photo-1574629810360-7efbbe195018?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80') center/cover,
        rgba(255, 255, 255, 0.9);
    background-blend-mode: overlay;
    background-attachment: scroll; /* Changed to scroll for better mobile compatibility */
}

/* Login Page Variant */
.fullwidth-auth-page.login .fullwidth-auth-main {
    background: linear-gradient(135deg, rgba(44, 95, 45, 0.1) 0%, rgba(82, 183, 136, 0.1) 100%),
                url('https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80') center/cover;
    background-attachment: scroll; /* Changed to scroll for better mobile compatibility */
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    .fullwidth-auth-main {
        padding: 1.5rem 1rem;
        background-attachment: scroll;
        height: 100%; /* Use height instead of min-height for scroll fix */
        align-items: flex-start; /* Start from top on mobile for better scrolling */
        overflow-y: auto; /* Ensure scrolling is enabled */
    }

    .fullwidth-auth-main::before {
        background: rgba(255, 255, 255, 0.95);
    }

    .fullwidth-auth-form-section {
        padding: 2rem 1.5rem;
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-md);
        background: var(--white);
        margin: 1rem 0 2rem 0; /* Add bottom margin for better scrolling */
        width: 100%;
        max-width: 100%;
    }

    .fullwidth-auth-title {
        font-size: 1.5rem;
    }

    .fullwidth-auth-subtitle {
        font-size: 0.9375rem;
    }

    .fullwidth-auth-header-content,
    .fullwidth-auth-footer-content {
        padding: 0 1rem;
    }
}

@media screen and (max-width: 480px) {
    .fullwidth-auth-page {
        min-height: 100vh;
        height: auto;
        overflow-y: auto; /* Enable page-level scrolling */
    }

    .fullwidth-auth-main {
        padding: 1rem 0.75rem;
        background-attachment: scroll;
        height: 100%; /* Use height instead of min-height for scroll fix */
        align-items: flex-start;
        flex: 1; /* Allow flex growth for proper scrolling */
        overflow-y: auto; /* Ensure scrolling capability */
    }

    .fullwidth-auth-main::before {
        background: rgba(255, 255, 255, 0.98);
    }

    .fullwidth-auth-form-section {
        padding: 1.5rem 1rem;
        box-shadow: var(--shadow-sm);
        border-radius: var(--radius-md);
        margin: 0.5rem 0 3rem 0; /* Increased bottom margin for better scrolling */
        width: 100%;
        max-width: 100%;
    }

    .fullwidth-auth-title {
        font-size: 1.375rem;
    }

    .fullwidth-auth-logo {
        font-size: 1.25rem;
    }

    .fullwidth-auth-header-content,
    .fullwidth-auth-footer-content {
        padding: 0 0.75rem;
    }
}

/* Extra small screens - Enhanced scrolling support */
@media screen and (max-width: 360px) {
    .fullwidth-auth-page {
        overflow-y: auto;
    }

    .fullwidth-auth-main {
        padding: 0.75rem 0.5rem;
        min-height: calc(100vh - 100px);
    }

    .fullwidth-auth-form-section {
        padding: 1.25rem 0.75rem;
        margin: 0.25rem 0 4rem 0; /* Extra bottom margin for very small screens */
    }
}

/* Large screens */
@media screen and (min-width: 1024px) {
    .fullwidth-auth-form-section {
        max-width: 520px;
        padding: 4rem 3rem;
    }

    .fullwidth-auth-title {
        font-size: 2rem;
    }
}


