{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\JoinChallenge.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport axios from '../utils/axiosConfig';\nimport { useTeamLogos } from '../utils/teamLogoUtils';\nimport './JoinChallenge.css';\nimport '../styles/TeamLogo.css';\n\n// Remove API_BASE_URL since axios is already configured with the correct base URL\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction JoinChallenge() {\n  _s();\n  const {\n    challengeId\n  } = useParams();\n  const navigate = useNavigate();\n  const [challenge, setChallenge] = useState(null);\n  const [selectedOutcome, setSelectedOutcome] = useState('');\n  const [betAmount, setBetAmount] = useState('');\n  const [errorMessage, setErrorMessage] = useState(null);\n  const [successMessage, setSuccessMessage] = useState(null);\n  const [teams, setTeams] = useState([]);\n  const [potentialReturns, setPotentialReturns] = useState({\n    win: 0,\n    draw: (challenge === null || challenge === void 0 ? void 0 : challenge.odds_draw) || 0,\n    loss: (challenge === null || challenge === void 0 ? void 0 : challenge.odds_lost) || 0\n  });\n\n  // Use the team logo utility\n  const {\n    getLogoUrl\n  } = useTeamLogos(teams);\n\n  // Improved teams fetching with retry\n  const fetchTeams = useCallback(async (retryCount = 0) => {\n    try {\n      const response = await axios.get(`/handlers/team_management.php`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('userToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data.status === 200) {\n        setTeams(response.data.data);\n      }\n    } catch (err) {\n      console.error('Error fetching teams:', err);\n      if (retryCount < 2) {\n        setTimeout(() => fetchTeams(retryCount + 1), 1000);\n      }\n    }\n  }, []);\n\n  // Improved challenge fetching with retry and better response handling\n  const fetchChallengeDetails = useCallback(async (retryCount = 0) => {\n    try {\n      const response = await axios.get(`/handlers/get_challenges.php`, {\n        params: {\n          id: challengeId\n        },\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('userToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data.success) {\n        if (response.data.challenge) {\n          setChallenge(response.data.challenge);\n          setErrorMessage(null);\n          return;\n        }\n      }\n\n      // If we reach here, no challenge was found\n      if (retryCount < 2) {\n        setTimeout(() => fetchChallengeDetails(retryCount + 1), 1000);\n      } else {\n        setErrorMessage('Challenge not found. Please try again.');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error fetching challenge details:', error);\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to load challenge details. Please refresh the page.';\n      setErrorMessage(errorMessage);\n      if (retryCount < 2) {\n        setTimeout(() => fetchChallengeDetails(retryCount + 1), 1000);\n      }\n    }\n  }, [challengeId]);\n  const calculatePotentialReturns = useCallback(() => {\n    if (!betAmount || !challenge) return;\n    const amount = parseFloat(betAmount);\n    let winOdds = 0;\n    try {\n      if (selectedOutcome === 'team_a_win') {\n        winOdds = parseFloat(challenge.odds_team_a) || 0;\n      } else if (selectedOutcome === 'team_b_win') {\n        winOdds = parseFloat(challenge.odds_team_b) || 0;\n      } else if (selectedOutcome === 'draw') {\n        winOdds = parseFloat(challenge.odds_draw) || 0;\n      } else {\n        return;\n      }\n      const drawOdds = parseFloat(challenge.odds_draw) || 0.9;\n      const lossOdds = parseFloat(challenge.odds_lost) || 0.2;\n      setPotentialReturns({\n        win: (amount * winOdds).toFixed(2),\n        draw: (amount * drawOdds).toFixed(2),\n        loss: (amount * lossOdds).toFixed(2)\n      });\n    } catch (err) {\n      console.error('Error calculating returns:', err);\n    }\n  }, [betAmount, selectedOutcome, challenge]);\n  useEffect(() => {\n    if (challengeId) {\n      fetchChallengeDetails();\n      fetchTeams();\n    }\n  }, [fetchChallengeDetails, fetchTeams, challengeId]);\n  useEffect(() => {\n    calculatePotentialReturns();\n  }, [calculatePotentialReturns]);\n\n  // Remove the old getTeamLogo function - now using the utility\n\n  const handleSubmitBet = async event => {\n    event.preventDefault();\n    setErrorMessage(null);\n    if (!selectedOutcome || !betAmount) {\n      setErrorMessage('Please select an outcome and enter a bet amount.');\n      return;\n    }\n    try {\n      const userId = localStorage.getItem('userId');\n      let odds;\n      if (selectedOutcome === 'team_a_win') {\n        odds = challenge.odds_team_a;\n      } else if (selectedOutcome === 'team_b_win') {\n        odds = challenge.odds_team_b;\n      } else if (selectedOutcome === 'draw') {\n        odds = challenge.odds_draw;\n      }\n      const amount = parseFloat(betAmount);\n      const winAmount = amount * odds;\n      const drawAmount = amount * (challenge.odds_draw || 0.9);\n      const lossAmount = amount * (challenge.odds_lost || 0.2);\n      const response = await axios.post('place_bet.php', {\n        challengeId,\n        userId,\n        outcome: selectedOutcome,\n        amount: betAmount,\n        odds,\n        potential_return_user1: winAmount,\n        potential_return_win_user1: winAmount,\n        potential_return_draw_user1: drawAmount,\n        potential_return_loss_user1: lossAmount\n      }, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('userToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data.success) {\n        const betId = response.data.bet_id;\n        setSuccessMessage('Bet placed successfully! Redirecting to your bets...');\n        // Clear form\n        setSelectedOutcome('');\n        setBetAmount('');\n\n        // Use the correct route path and ensure state updates are complete\n        const timer = setTimeout(() => {\n          navigate('/user/bets/outgoing', {\n            state: {\n              newBetId: betId\n            },\n            replace: true // Replace current history entry\n          });\n        }, 4000);\n\n        // Cleanup timer if component unmounts\n        return () => clearTimeout(timer);\n      } else {\n        throw new Error(response.data.message || 'Failed to place bet');\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Error placing bet:', error);\n      const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'An error occurred while placing your bet.';\n      setErrorMessage(errorMessage);\n    }\n  };\n  if (!challenge && !errorMessage) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"join-challenge\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading challenge details...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 44\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"join-challenge\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Join Challenge\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this), errorMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: errorMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 24\n    }, this), successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success-message\",\n      children: successMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 26\n    }, this), challenge && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"centered-layout\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"challenge-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"team-info\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"challenge-team-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: getLogoUrl(challenge.team_a),\n              alt: challenge.team_a,\n              className: \"challenge-team-logo\",\n              onError: e => {\n                e.target.src = '/images/default-team-logo.png';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"challenge-team-name\",\n              children: challenge.team_a\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"vs-container\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"vs\",\n            children: \"VS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"team-info\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"challenge-team-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: getTeamLogo(challenge.team_b),\n              alt: challenge.team_b,\n              className: \"challenge-team-logo\",\n              onError: e => {\n                e.target.src = '/default-team-logo.png';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"challenge-team-name\",\n              children: challenge.team_b\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"match-details-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-item match-type\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"label\",\n            children: \"Match Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"value\",\n            children: challenge.match_type === 'full_time' ? 'Full Time' : 'Half Time'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-item odds\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"label\",\n            children: [challenge.team_a, \" Odds\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"value\",\n            children: challenge.odds_team_a\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-item odds\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"label\",\n            children: [challenge.team_b, \" Odds\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"value\",\n            children: challenge.odds_team_b\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-item odds\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"label\",\n            children: \"Draw Odds\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"value\",\n            children: challenge.odds_draw\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-item odds\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"label\",\n            children: \"Loss Odds\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"value\",\n            children: challenge.odds_lost\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"label\",\n            children: \"Goal Advantage\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"value\",\n            children: [challenge.team_a_goal_advantage, \" - \", challenge.team_b_goal_advantage]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-item date\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"label\",\n            children: \"Match Date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"value\",\n            children: new Date(challenge.match_date).toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"bet-form\",\n        onSubmit: handleSubmitBet,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"outcome\",\n            children: \"Select Outcome:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            id: \"outcome\",\n            value: selectedOutcome,\n            onChange: e => setSelectedOutcome(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"-- Select --\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"team_a_win\",\n              children: [\"Home Win (\", challenge.team_a, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"team_b_win\",\n              children: [\"Away Win (\", challenge.team_b, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"draw\",\n              children: \"Draw\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"amount\",\n            children: \"Bet Amount (FanCoins):\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            id: \"amount\",\n            value: betAmount,\n            onChange: e => setBetAmount(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this), betAmount && selectedOutcome && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"potential-returns\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Expected Returns\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"returns-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"return-item win\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"label\",\n                children: \"Win\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"value-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"value\",\n                  children: Number(potentialReturns.win).toFixed(2)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"currency\",\n                  children: \"FanCoins\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"return-item draw\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"label\",\n                children: \"Draw\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"value-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"value\",\n                  children: Number(potentialReturns.draw).toFixed(2)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"currency\",\n                  children: \"FanCoins\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"return-item loss\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"label\",\n                children: \"Loss Return\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"value-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"value\",\n                  children: Number(potentialReturns.loss).toFixed(2)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"currency\",\n                  children: \"FanCoins\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          children: \"Place Bet\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 199,\n    columnNumber: 5\n  }, this);\n}\n_s(JoinChallenge, \"NwiM1d6AXoaeciS4vSJaflVQM5U=\", false, function () {\n  return [useParams, useNavigate, useTeamLogos];\n});\n_c = JoinChallenge;\nexport default JoinChallenge;\nvar _c;\n$RefreshReg$(_c, \"JoinChallenge\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useParams", "useNavigate", "axios", "useTeamLogos", "jsxDEV", "_jsxDEV", "JoinChall<PERSON>e", "_s", "challengeId", "navigate", "challenge", "setChallenge", "selectedOutcome", "setSelectedOutcome", "betAmount", "setBetAmount", "errorMessage", "setErrorMessage", "successMessage", "setSuccessMessage", "teams", "setTeams", "potentialReturns", "setPotentialReturns", "win", "draw", "odds_draw", "loss", "odds_lost", "getLogoUrl", "fetchTeams", "retryCount", "response", "get", "headers", "localStorage", "getItem", "data", "status", "err", "console", "error", "setTimeout", "fetchChallengeDetails", "params", "id", "success", "_error$response", "_error$response$data", "message", "calculatePotentialReturns", "amount", "parseFloat", "winOdds", "odds_team_a", "odds_team_b", "drawOdds", "lossOdds", "toFixed", "handleSubmitBet", "event", "preventDefault", "userId", "odds", "winAmount", "drawAmount", "lossAmount", "post", "outcome", "potential_return_user1", "potential_return_win_user1", "potential_return_draw_user1", "potential_return_loss_user1", "betId", "bet_id", "timer", "state", "newBetId", "replace", "clearTimeout", "Error", "_error$response2", "_error$response2$data", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "team_a", "alt", "onError", "e", "target", "getTeamLogo", "team_b", "match_type", "team_a_goal_advantage", "team_b_goal_advantage", "Date", "match_date", "toLocaleString", "onSubmit", "htmlFor", "value", "onChange", "type", "Number", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/JoinChallenge.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport axios from '../utils/axiosConfig';\nimport { useTeamLogos } from '../utils/teamLogoUtils';\nimport './JoinChallenge.css';\nimport '../styles/TeamLogo.css';\n\n// Remove API_BASE_URL since axios is already configured with the correct base URL\n\nfunction JoinChallenge() {\n  const { challengeId } = useParams();\n  const navigate = useNavigate();\n  const [challenge, setChallenge] = useState(null);\n  const [selectedOutcome, setSelectedOutcome] = useState('');\n  const [betAmount, setBetAmount] = useState('');\n  const [errorMessage, setErrorMessage] = useState(null);\n  const [successMessage, setSuccessMessage] = useState(null);\n  const [teams, setTeams] = useState([]);\n  const [potentialReturns, setPotentialReturns] = useState({\n    win: 0,\n    draw: challenge?.odds_draw || 0,\n    loss: challenge?.odds_lost || 0\n  });\n\n  // Use the team logo utility\n  const { getLogoUrl } = useTeamLogos(teams);\n\n  // Improved teams fetching with retry\n  const fetchTeams = useCallback(async (retryCount = 0) => {\n    try {\n      const response = await axios.get(`/handlers/team_management.php`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('userToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data.status === 200) {\n        setTeams(response.data.data);\n      }\n    } catch (err) {\n      console.error('Error fetching teams:', err);\n      if (retryCount < 2) {\n        setTimeout(() => fetchTeams(retryCount + 1), 1000);\n      }\n    }\n  }, []);\n\n  // Improved challenge fetching with retry and better response handling\n  const fetchChallengeDetails = useCallback(async (retryCount = 0) => {\n    try {\n      const response = await axios.get(`/handlers/get_challenges.php`, {\n        params: { id: challengeId },\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('userToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      \n      if (response.data.success) {\n        if (response.data.challenge) {\n          setChallenge(response.data.challenge);\n          setErrorMessage(null);\n          return;\n        }\n      }\n      \n      // If we reach here, no challenge was found\n      if (retryCount < 2) {\n        setTimeout(() => fetchChallengeDetails(retryCount + 1), 1000);\n      } else {\n        setErrorMessage('Challenge not found. Please try again.');\n      }\n    } catch (error) {\n      console.error('Error fetching challenge details:', error);\n      const errorMessage = error.response?.data?.message || 'Failed to load challenge details. Please refresh the page.';\n      setErrorMessage(errorMessage);\n      if (retryCount < 2) {\n        setTimeout(() => fetchChallengeDetails(retryCount + 1), 1000);\n      }\n    }\n  }, [challengeId]);\n\n  const calculatePotentialReturns = useCallback(() => {\n    if (!betAmount || !challenge) return;\n    const amount = parseFloat(betAmount);\n    let winOdds = 0;\n    \n    try {\n      if (selectedOutcome === 'team_a_win') {\n        winOdds = parseFloat(challenge.odds_team_a) || 0;\n      } else if (selectedOutcome === 'team_b_win') {\n        winOdds = parseFloat(challenge.odds_team_b) || 0;\n      } else if (selectedOutcome === 'draw') {\n        winOdds = parseFloat(challenge.odds_draw) || 0;\n      } else {\n        return;\n      }\n\n      const drawOdds = parseFloat(challenge.odds_draw) || 0.9;\n      const lossOdds = parseFloat(challenge.odds_lost) || 0.2;\n\n      setPotentialReturns({\n        win: (amount * winOdds).toFixed(2),\n        draw: (amount * drawOdds).toFixed(2),\n        loss: (amount * lossOdds).toFixed(2),\n      });\n    } catch (err) {\n      console.error('Error calculating returns:', err);\n    }\n  }, [betAmount, selectedOutcome, challenge]);\n\n  useEffect(() => {\n    if (challengeId) {\n      fetchChallengeDetails();\n      fetchTeams();\n    }\n  }, [fetchChallengeDetails, fetchTeams, challengeId]);\n\n  useEffect(() => {\n    calculatePotentialReturns();\n  }, [calculatePotentialReturns]);\n\n  // Remove the old getTeamLogo function - now using the utility\n\n  const handleSubmitBet = async (event) => {\n    event.preventDefault();\n    setErrorMessage(null);\n\n    if (!selectedOutcome || !betAmount) {\n      setErrorMessage('Please select an outcome and enter a bet amount.');\n      return;\n    }\n\n    try {\n      const userId = localStorage.getItem('userId');\n      let odds;\n      if (selectedOutcome === 'team_a_win') {\n        odds = challenge.odds_team_a;\n      } else if (selectedOutcome === 'team_b_win') {\n        odds = challenge.odds_team_b;\n      } else if (selectedOutcome === 'draw') {\n        odds = challenge.odds_draw;\n      }\n      \n      const amount = parseFloat(betAmount);\n      const winAmount = amount * odds;\n      const drawAmount = amount * (challenge.odds_draw || 0.9);\n      const lossAmount = amount * (challenge.odds_lost || 0.2);\n\n      const response = await axios.post('place_bet.php', {\n        challengeId,\n        userId,\n        outcome: selectedOutcome,\n        amount: betAmount,\n        odds,\n        potential_return_user1: winAmount,\n        potential_return_win_user1: winAmount,\n        potential_return_draw_user1: drawAmount,\n        potential_return_loss_user1: lossAmount\n      }, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('userToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (response.data.success) {\n        const betId = response.data.bet_id;\n        setSuccessMessage('Bet placed successfully! Redirecting to your bets...');\n        // Clear form\n        setSelectedOutcome('');\n        setBetAmount('');\n        \n        // Use the correct route path and ensure state updates are complete\n        const timer = setTimeout(() => {\n          navigate('/user/bets/outgoing', { \n            state: { newBetId: betId },\n            replace: true  // Replace current history entry\n          });\n        }, 4000);\n        \n        // Cleanup timer if component unmounts\n        return () => clearTimeout(timer);\n      } else {\n        throw new Error(response.data.message || 'Failed to place bet');\n      }\n    } catch (error) {\n      console.error('Error placing bet:', error);\n      const errorMessage = error.response?.data?.message || 'An error occurred while placing your bet.';\n      setErrorMessage(errorMessage);\n    }\n  };\n\n  if (!challenge && !errorMessage) {\n    return <div className=\"join-challenge\"><p>Loading challenge details...</p></div>;\n  }\n\n  return (\n    <div className=\"join-challenge\">\n      <h1>Join Challenge</h1>\n      {errorMessage && <div className=\"error-message\">{errorMessage}</div>}\n      {successMessage && <div className=\"success-message\">{successMessage}</div>}\n      {challenge && (\n        <div className=\"centered-layout\">\n          <div className=\"challenge-details\">\n            <div className=\"team-info\">\n              <div className=\"challenge-team-wrapper\">\n                <img\n                  src={getLogoUrl(challenge.team_a)}\n                  alt={challenge.team_a}\n                  className=\"challenge-team-logo\"\n                  onError={(e) => {\n                    e.target.src = '/images/default-team-logo.png';\n                  }}\n                />\n                <span className=\"challenge-team-name\">{challenge.team_a}</span>\n              </div>\n            </div>\n            \n            <div className=\"vs-container\">\n              <span className=\"vs\">VS</span>\n            </div>\n            \n            <div className=\"team-info\">\n              <div className=\"challenge-team-wrapper\">\n                <img\n                  src={getTeamLogo(challenge.team_b)}\n                  alt={challenge.team_b}\n                  className=\"challenge-team-logo\"\n                  onError={(e) => {\n                    e.target.src = '/default-team-logo.png';\n                  }}\n                />\n                <span className=\"challenge-team-name\">{challenge.team_b}</span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"match-details-grid\">\n            <div className=\"detail-item match-type\">\n              <div className=\"label\">Match Type</div>\n              <div className=\"value\">{challenge.match_type === 'full_time' ? 'Full Time' : 'Half Time'}</div>\n            </div>\n            \n            <div className=\"detail-item odds\">\n              <div className=\"label\">{challenge.team_a} Odds</div>\n              <div className=\"value\">{challenge.odds_team_a}</div>\n            </div>\n            \n            <div className=\"detail-item odds\">\n              <div className=\"label\">{challenge.team_b} Odds</div>\n              <div className=\"value\">{challenge.odds_team_b}</div>\n            </div>\n            \n            <div className=\"detail-item odds\">\n              <div className=\"label\">Draw Odds</div>\n              <div className=\"value\">{challenge.odds_draw}</div>\n            </div>\n            \n            <div className=\"detail-item odds\">\n              <div className=\"label\">Loss Odds</div>\n              <div className=\"value\">{challenge.odds_lost}</div>\n            </div>\n            \n            <div className=\"detail-item\">\n              <div className=\"label\">Goal Advantage</div>\n              <div className=\"value\">{challenge.team_a_goal_advantage} - {challenge.team_b_goal_advantage}</div>\n            </div>\n            \n            <div className=\"detail-item date\">\n              <div className=\"label\">Match Date</div>\n              <div className=\"value\">{new Date(challenge.match_date).toLocaleString()}</div>\n            </div>\n          </div>\n\n          <form className=\"bet-form\" onSubmit={handleSubmitBet}>\n            <div>\n              <label htmlFor=\"outcome\">Select Outcome:</label>\n              <select id=\"outcome\" value={selectedOutcome} onChange={(e) => setSelectedOutcome(e.target.value)}>\n                <option value=\"\">-- Select --</option>\n                <option value=\"team_a_win\">Home Win ({challenge.team_a})</option>\n                <option value=\"team_b_win\">Away Win ({challenge.team_b})</option>\n                <option value=\"draw\">Draw</option>\n              </select>\n            </div>\n            <div>\n              <label htmlFor=\"amount\">Bet Amount (FanCoins):</label>\n              <input\n                type=\"number\"\n                id=\"amount\"\n                value={betAmount}\n                onChange={(e) => setBetAmount(e.target.value)}\n              />\n            </div>\n            {betAmount && selectedOutcome && (\n              <div className=\"potential-returns\">\n                <h3>Expected Returns</h3>\n                <div className=\"returns-grid\">\n                  <div className=\"return-item win\">\n                    <div className=\"label\">Win</div>\n                    <div className=\"value-container\">\n                      <div className=\"value\">{Number(potentialReturns.win).toFixed(2)}</div>\n                      <div className=\"currency\">FanCoins</div>\n                    </div>\n                  </div>\n                  <div className=\"return-item draw\">\n                    <div className=\"label\">Draw</div>\n                    <div className=\"value-container\">\n                      <div className=\"value\">{Number(potentialReturns.draw).toFixed(2)}</div>\n                      <div className=\"currency\">FanCoins</div>\n                    </div>\n                  </div>\n                  <div className=\"return-item loss\">\n                    <div className=\"label\">Loss Return</div>\n                    <div className=\"value-container\">\n                      <div className=\"value\">{Number(potentialReturns.loss).toFixed(2)}</div>\n                      <div className=\"currency\">FanCoins</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n            <button type=\"submit\">Place Bet</button>\n          </form>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default JoinChallenge;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAO,qBAAqB;AAC5B,OAAO,wBAAwB;;AAE/B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACvB,MAAM;IAAEC;EAAY,CAAC,GAAGR,SAAS,CAAC,CAAC;EACnC,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC;IACvD2B,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAAf,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEgB,SAAS,KAAI,CAAC;IAC/BC,IAAI,EAAE,CAAAjB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEkB,SAAS,KAAI;EAChC,CAAC,CAAC;;EAEF;EACA,MAAM;IAAEC;EAAW,CAAC,GAAG1B,YAAY,CAACiB,KAAK,CAAC;;EAE1C;EACA,MAAMU,UAAU,GAAG/B,WAAW,CAAC,OAAOgC,UAAU,GAAG,CAAC,KAAK;IACvD,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM9B,KAAK,CAAC+B,GAAG,CAAC,+BAA+B,EAAE;QAChEC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,EAAE;UAC9D,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,IAAIJ,QAAQ,CAACK,IAAI,CAACC,MAAM,KAAK,GAAG,EAAE;QAChCjB,QAAQ,CAACW,QAAQ,CAACK,IAAI,CAACA,IAAI,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,GAAG,CAAC;MAC3C,IAAIR,UAAU,GAAG,CAAC,EAAE;QAClBW,UAAU,CAAC,MAAMZ,UAAU,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;MACpD;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMY,qBAAqB,GAAG5C,WAAW,CAAC,OAAOgC,UAAU,GAAG,CAAC,KAAK;IAClE,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM9B,KAAK,CAAC+B,GAAG,CAAC,8BAA8B,EAAE;QAC/DW,MAAM,EAAE;UAAEC,EAAE,EAAErC;QAAY,CAAC;QAC3B0B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,EAAE;UAC9D,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAIJ,QAAQ,CAACK,IAAI,CAACS,OAAO,EAAE;QACzB,IAAId,QAAQ,CAACK,IAAI,CAAC3B,SAAS,EAAE;UAC3BC,YAAY,CAACqB,QAAQ,CAACK,IAAI,CAAC3B,SAAS,CAAC;UACrCO,eAAe,CAAC,IAAI,CAAC;UACrB;QACF;MACF;;MAEA;MACA,IAAIc,UAAU,GAAG,CAAC,EAAE;QAClBW,UAAU,CAAC,MAAMC,qBAAqB,CAACZ,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;MAC/D,CAAC,MAAM;QACLd,eAAe,CAAC,wCAAwC,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOwB,KAAK,EAAE;MAAA,IAAAM,eAAA,EAAAC,oBAAA;MACdR,OAAO,CAACC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAMzB,YAAY,GAAG,EAAA+B,eAAA,GAAAN,KAAK,CAACT,QAAQ,cAAAe,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBV,IAAI,cAAAW,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,4DAA4D;MAClHhC,eAAe,CAACD,YAAY,CAAC;MAC7B,IAAIe,UAAU,GAAG,CAAC,EAAE;QAClBW,UAAU,CAAC,MAAMC,qBAAqB,CAACZ,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;MAC/D;IACF;EACF,CAAC,EAAE,CAACvB,WAAW,CAAC,CAAC;EAEjB,MAAM0C,yBAAyB,GAAGnD,WAAW,CAAC,MAAM;IAClD,IAAI,CAACe,SAAS,IAAI,CAACJ,SAAS,EAAE;IAC9B,MAAMyC,MAAM,GAAGC,UAAU,CAACtC,SAAS,CAAC;IACpC,IAAIuC,OAAO,GAAG,CAAC;IAEf,IAAI;MACF,IAAIzC,eAAe,KAAK,YAAY,EAAE;QACpCyC,OAAO,GAAGD,UAAU,CAAC1C,SAAS,CAAC4C,WAAW,CAAC,IAAI,CAAC;MAClD,CAAC,MAAM,IAAI1C,eAAe,KAAK,YAAY,EAAE;QAC3CyC,OAAO,GAAGD,UAAU,CAAC1C,SAAS,CAAC6C,WAAW,CAAC,IAAI,CAAC;MAClD,CAAC,MAAM,IAAI3C,eAAe,KAAK,MAAM,EAAE;QACrCyC,OAAO,GAAGD,UAAU,CAAC1C,SAAS,CAACgB,SAAS,CAAC,IAAI,CAAC;MAChD,CAAC,MAAM;QACL;MACF;MAEA,MAAM8B,QAAQ,GAAGJ,UAAU,CAAC1C,SAAS,CAACgB,SAAS,CAAC,IAAI,GAAG;MACvD,MAAM+B,QAAQ,GAAGL,UAAU,CAAC1C,SAAS,CAACkB,SAAS,CAAC,IAAI,GAAG;MAEvDL,mBAAmB,CAAC;QAClBC,GAAG,EAAE,CAAC2B,MAAM,GAAGE,OAAO,EAAEK,OAAO,CAAC,CAAC,CAAC;QAClCjC,IAAI,EAAE,CAAC0B,MAAM,GAAGK,QAAQ,EAAEE,OAAO,CAAC,CAAC,CAAC;QACpC/B,IAAI,EAAE,CAACwB,MAAM,GAAGM,QAAQ,EAAEC,OAAO,CAAC,CAAC;MACrC,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOnB,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEF,GAAG,CAAC;IAClD;EACF,CAAC,EAAE,CAACzB,SAAS,EAAEF,eAAe,EAAEF,SAAS,CAAC,CAAC;EAE3CZ,SAAS,CAAC,MAAM;IACd,IAAIU,WAAW,EAAE;MACfmC,qBAAqB,CAAC,CAAC;MACvBb,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACa,qBAAqB,EAAEb,UAAU,EAAEtB,WAAW,CAAC,CAAC;EAEpDV,SAAS,CAAC,MAAM;IACdoD,yBAAyB,CAAC,CAAC;EAC7B,CAAC,EAAE,CAACA,yBAAyB,CAAC,CAAC;;EAE/B;;EAEA,MAAMS,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvCA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB5C,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI,CAACL,eAAe,IAAI,CAACE,SAAS,EAAE;MAClCG,eAAe,CAAC,kDAAkD,CAAC;MACnE;IACF;IAEA,IAAI;MACF,MAAM6C,MAAM,GAAG3B,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;MAC7C,IAAI2B,IAAI;MACR,IAAInD,eAAe,KAAK,YAAY,EAAE;QACpCmD,IAAI,GAAGrD,SAAS,CAAC4C,WAAW;MAC9B,CAAC,MAAM,IAAI1C,eAAe,KAAK,YAAY,EAAE;QAC3CmD,IAAI,GAAGrD,SAAS,CAAC6C,WAAW;MAC9B,CAAC,MAAM,IAAI3C,eAAe,KAAK,MAAM,EAAE;QACrCmD,IAAI,GAAGrD,SAAS,CAACgB,SAAS;MAC5B;MAEA,MAAMyB,MAAM,GAAGC,UAAU,CAACtC,SAAS,CAAC;MACpC,MAAMkD,SAAS,GAAGb,MAAM,GAAGY,IAAI;MAC/B,MAAME,UAAU,GAAGd,MAAM,IAAIzC,SAAS,CAACgB,SAAS,IAAI,GAAG,CAAC;MACxD,MAAMwC,UAAU,GAAGf,MAAM,IAAIzC,SAAS,CAACkB,SAAS,IAAI,GAAG,CAAC;MAExD,MAAMI,QAAQ,GAAG,MAAM9B,KAAK,CAACiE,IAAI,CAAC,eAAe,EAAE;QACjD3D,WAAW;QACXsD,MAAM;QACNM,OAAO,EAAExD,eAAe;QACxBuC,MAAM,EAAErC,SAAS;QACjBiD,IAAI;QACJM,sBAAsB,EAAEL,SAAS;QACjCM,0BAA0B,EAAEN,SAAS;QACrCO,2BAA2B,EAAEN,UAAU;QACvCO,2BAA2B,EAAEN;MAC/B,CAAC,EAAE;QACDhC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,EAAE;UAC9D,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAIJ,QAAQ,CAACK,IAAI,CAACS,OAAO,EAAE;QACzB,MAAM2B,KAAK,GAAGzC,QAAQ,CAACK,IAAI,CAACqC,MAAM;QAClCvD,iBAAiB,CAAC,sDAAsD,CAAC;QACzE;QACAN,kBAAkB,CAAC,EAAE,CAAC;QACtBE,YAAY,CAAC,EAAE,CAAC;;QAEhB;QACA,MAAM4D,KAAK,GAAGjC,UAAU,CAAC,MAAM;UAC7BjC,QAAQ,CAAC,qBAAqB,EAAE;YAC9BmE,KAAK,EAAE;cAAEC,QAAQ,EAAEJ;YAAM,CAAC;YAC1BK,OAAO,EAAE,IAAI,CAAE;UACjB,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;;QAER;QACA,OAAO,MAAMC,YAAY,CAACJ,KAAK,CAAC;MAClC,CAAC,MAAM;QACL,MAAM,IAAIK,KAAK,CAAChD,QAAQ,CAACK,IAAI,CAACY,OAAO,IAAI,qBAAqB,CAAC;MACjE;IACF,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAAwC,gBAAA,EAAAC,qBAAA;MACd1C,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMzB,YAAY,GAAG,EAAAiE,gBAAA,GAAAxC,KAAK,CAACT,QAAQ,cAAAiD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5C,IAAI,cAAA6C,qBAAA,uBAApBA,qBAAA,CAAsBjC,OAAO,KAAI,2CAA2C;MACjGhC,eAAe,CAACD,YAAY,CAAC;IAC/B;EACF,CAAC;EAED,IAAI,CAACN,SAAS,IAAI,CAACM,YAAY,EAAE;IAC/B,oBAAOX,OAAA;MAAK8E,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAAC/E,OAAA;QAAA+E,QAAA,EAAG;MAA4B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAClF;EAEA,oBACEnF,OAAA;IAAK8E,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B/E,OAAA;MAAA+E,QAAA,EAAI;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EACtBxE,YAAY,iBAAIX,OAAA;MAAK8E,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAEpE;IAAY;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACnEtE,cAAc,iBAAIb,OAAA;MAAK8E,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAAElE;IAAc;MAAAmE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACzE9E,SAAS,iBACRL,OAAA;MAAK8E,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B/E,OAAA;QAAK8E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC/E,OAAA;UAAK8E,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB/E,OAAA;YAAK8E,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC/E,OAAA;cACEoF,GAAG,EAAE5D,UAAU,CAACnB,SAAS,CAACgF,MAAM,CAAE;cAClCC,GAAG,EAAEjF,SAAS,CAACgF,MAAO;cACtBP,SAAS,EAAC,qBAAqB;cAC/BS,OAAO,EAAGC,CAAC,IAAK;gBACdA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,+BAA+B;cAChD;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFnF,OAAA;cAAM8E,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAE1E,SAAS,CAACgF;YAAM;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnF,OAAA;UAAK8E,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B/E,OAAA;YAAM8E,SAAS,EAAC,IAAI;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAENnF,OAAA;UAAK8E,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB/E,OAAA;YAAK8E,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC/E,OAAA;cACEoF,GAAG,EAAEM,WAAW,CAACrF,SAAS,CAACsF,MAAM,CAAE;cACnCL,GAAG,EAAEjF,SAAS,CAACsF,MAAO;cACtBb,SAAS,EAAC,qBAAqB;cAC/BS,OAAO,EAAGC,CAAC,IAAK;gBACdA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,wBAAwB;cACzC;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFnF,OAAA;cAAM8E,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAE1E,SAAS,CAACsF;YAAM;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnF,OAAA;QAAK8E,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC/E,OAAA;UAAK8E,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC/E,OAAA;YAAK8E,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCnF,OAAA;YAAK8E,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAE1E,SAAS,CAACuF,UAAU,KAAK,WAAW,GAAG,WAAW,GAAG;UAAW;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5F,CAAC,eAENnF,OAAA;UAAK8E,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B/E,OAAA;YAAK8E,SAAS,EAAC,OAAO;YAAAC,QAAA,GAAE1E,SAAS,CAACgF,MAAM,EAAC,OAAK;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpDnF,OAAA;YAAK8E,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAE1E,SAAS,CAAC4C;UAAW;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eAENnF,OAAA;UAAK8E,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B/E,OAAA;YAAK8E,SAAS,EAAC,OAAO;YAAAC,QAAA,GAAE1E,SAAS,CAACsF,MAAM,EAAC,OAAK;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpDnF,OAAA;YAAK8E,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAE1E,SAAS,CAAC6C;UAAW;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eAENnF,OAAA;UAAK8E,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B/E,OAAA;YAAK8E,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCnF,OAAA;YAAK8E,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAE1E,SAAS,CAACgB;UAAS;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eAENnF,OAAA;UAAK8E,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B/E,OAAA;YAAK8E,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCnF,OAAA;YAAK8E,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAE1E,SAAS,CAACkB;UAAS;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eAENnF,OAAA;UAAK8E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B/E,OAAA;YAAK8E,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3CnF,OAAA;YAAK8E,SAAS,EAAC,OAAO;YAAAC,QAAA,GAAE1E,SAAS,CAACwF,qBAAqB,EAAC,KAAG,EAACxF,SAAS,CAACyF,qBAAqB;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/F,CAAC,eAENnF,OAAA;UAAK8E,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B/E,OAAA;YAAK8E,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCnF,OAAA;YAAK8E,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAE,IAAIgB,IAAI,CAAC1F,SAAS,CAAC2F,UAAU,CAAC,CAACC,cAAc,CAAC;UAAC;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnF,OAAA;QAAM8E,SAAS,EAAC,UAAU;QAACoB,QAAQ,EAAE5C,eAAgB;QAAAyB,QAAA,gBACnD/E,OAAA;UAAA+E,QAAA,gBACE/E,OAAA;YAAOmG,OAAO,EAAC,SAAS;YAAApB,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChDnF,OAAA;YAAQwC,EAAE,EAAC,SAAS;YAAC4D,KAAK,EAAE7F,eAAgB;YAAC8F,QAAQ,EAAGb,CAAC,IAAKhF,kBAAkB,CAACgF,CAAC,CAACC,MAAM,CAACW,KAAK,CAAE;YAAArB,QAAA,gBAC/F/E,OAAA;cAAQoG,KAAK,EAAC,EAAE;cAAArB,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCnF,OAAA;cAAQoG,KAAK,EAAC,YAAY;cAAArB,QAAA,GAAC,YAAU,EAAC1E,SAAS,CAACgF,MAAM,EAAC,GAAC;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjEnF,OAAA;cAAQoG,KAAK,EAAC,YAAY;cAAArB,QAAA,GAAC,YAAU,EAAC1E,SAAS,CAACsF,MAAM,EAAC,GAAC;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjEnF,OAAA;cAAQoG,KAAK,EAAC,MAAM;cAAArB,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNnF,OAAA;UAAA+E,QAAA,gBACE/E,OAAA;YAAOmG,OAAO,EAAC,QAAQ;YAAApB,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtDnF,OAAA;YACEsG,IAAI,EAAC,QAAQ;YACb9D,EAAE,EAAC,QAAQ;YACX4D,KAAK,EAAE3F,SAAU;YACjB4F,QAAQ,EAAGb,CAAC,IAAK9E,YAAY,CAAC8E,CAAC,CAACC,MAAM,CAACW,KAAK;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EACL1E,SAAS,IAAIF,eAAe,iBAC3BP,OAAA;UAAK8E,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC/E,OAAA;YAAA+E,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBnF,OAAA;YAAK8E,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B/E,OAAA;cAAK8E,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B/E,OAAA;gBAAK8E,SAAS,EAAC,OAAO;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChCnF,OAAA;gBAAK8E,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B/E,OAAA;kBAAK8E,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAEwB,MAAM,CAACtF,gBAAgB,CAACE,GAAG,CAAC,CAACkC,OAAO,CAAC,CAAC;gBAAC;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtEnF,OAAA;kBAAK8E,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnF,OAAA;cAAK8E,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B/E,OAAA;gBAAK8E,SAAS,EAAC,OAAO;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjCnF,OAAA;gBAAK8E,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B/E,OAAA;kBAAK8E,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAEwB,MAAM,CAACtF,gBAAgB,CAACG,IAAI,CAAC,CAACiC,OAAO,CAAC,CAAC;gBAAC;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvEnF,OAAA;kBAAK8E,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnF,OAAA;cAAK8E,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B/E,OAAA;gBAAK8E,SAAS,EAAC,OAAO;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxCnF,OAAA;gBAAK8E,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B/E,OAAA;kBAAK8E,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAEwB,MAAM,CAACtF,gBAAgB,CAACK,IAAI,CAAC,CAAC+B,OAAO,CAAC,CAAC;gBAAC;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvEnF,OAAA;kBAAK8E,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eACDnF,OAAA;UAAQsG,IAAI,EAAC,QAAQ;UAAAvB,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACjF,EAAA,CA/TQD,aAAa;EAAA,QACIN,SAAS,EAChBC,WAAW,EAcLE,YAAY;AAAA;AAAA0G,EAAA,GAhB5BvG,aAAa;AAiUtB,eAAeA,aAAa;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}