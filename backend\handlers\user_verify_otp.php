<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Only POST method is allowed");
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $userId = $input['userId'] ?? null;
    $otpCode = $input['otp_code'] ?? '';
    
    if (!$userId) {
        throw new Exception("User ID is required");
    }
    
    if (empty($otpCode) || strlen($otpCode) !== 6 || !ctype_digit($otpCode)) {
        throw new Exception("Please provide a valid 6-digit OTP code");
    }
    
    // Verify user exists
    $stmt = $conn->prepare("SELECT user_id, username, email, otp_enabled FROM users WHERE user_id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception("User not found");
    }
    
    if (!$user['otp_enabled']) {
        throw new Exception("OTP is not enabled for this user");
    }
    
    // Check for rate limiting (max 5 attempts per 30 minutes)
    $stmt = $conn->prepare("
        SELECT attempts, locked_until, last_attempt 
        FROM user_login_attempts 
        WHERE user_id = ? AND attempt_type = 'otp' AND locked_until > NOW()
        ORDER BY last_attempt DESC 
        LIMIT 1
    ");
    $stmt->execute([$userId]);
    $rateLimitCheck = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($rateLimitCheck) {
        throw new Exception("Account is temporarily locked due to too many failed OTP attempts. Please try again later.");
    }
    
    // Get the most recent valid OTP for this user
    $currentTime = date('Y-m-d H:i:s');
    $stmt = $conn->prepare("
        SELECT id, otp, expiry, attempts, used
        FROM user_otp
        WHERE user_id = ? AND used = 0 AND expiry > ?
        ORDER BY created_at DESC
        LIMIT 1
    ");
    $stmt->execute([$userId, $currentTime]);
    $otpRecord = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$otpRecord) {
        throw new Exception("No valid OTP found. Please request a new OTP code.");
    }
    
    // Note: locked_until column doesn't exist in current table structure
    // This check is removed for now
    
    $conn->beginTransaction();
    
    if ($otpRecord['otp'] !== $otpCode) {
        // Handle failed attempt
        $newAttempts = $otpRecord['attempts'] + 1;
        
        // Update OTP attempts
        $stmt = $conn->prepare("UPDATE user_otp SET attempts = ? WHERE id = ?");
        $stmt->execute([$newAttempts, $otpRecord['id']]);
        
        // Update login attempts table
        $stmt = $conn->prepare("
            INSERT INTO user_login_attempts (user_id, ip_address, attempt_type, attempts) 
            VALUES (?, ?, 'otp', 1)
            ON DUPLICATE KEY UPDATE 
            attempts = attempts + 1, 
            last_attempt = NOW()
        ");
        $stmt->execute([$userId, $_SERVER['REMOTE_ADDR'] ?? 'unknown']);
        
        // Lock OTP if too many attempts (3 attempts per OTP)
        if ($newAttempts >= 3) {
            $lockedUntil = date('Y-m-d H:i:s', time() + 900); // 15 minutes
            $stmt = $conn->prepare("UPDATE user_otp SET locked_until = ? WHERE id = ?");
            $stmt->execute([$lockedUntil, $otpRecord['id']]);
        }
        
        // Check if we need to lock the account (5 total OTP attempts)
        $stmt = $conn->prepare("
            SELECT attempts FROM user_login_attempts 
            WHERE user_id = ? AND attempt_type = 'otp' 
            ORDER BY last_attempt DESC 
            LIMIT 1
        ");
        $stmt->execute([$userId]);
        $totalAttempts = $stmt->fetch(PDO::FETCH_ASSOC)['attempts'] ?? 0;
        
        if ($totalAttempts >= 5) {
            $lockedUntil = date('Y-m-d H:i:s', time() + 1800); // 30 minutes
            $stmt = $conn->prepare("
                UPDATE user_login_attempts 
                SET locked_until = ? 
                WHERE user_id = ? AND attempt_type = 'otp'
            ");
            $stmt->execute([$lockedUntil, $userId]);
            
            // Log lockout
            $stmt = $conn->prepare("
                INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
                VALUES (?, 'otp', 'account_locked', ?, ?, ?)
            ");
            $stmt->execute([
                $userId,
                json_encode(['reason' => 'max_otp_attempts_exceeded', 'locked_until' => $lockedUntil]),
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
            
            $conn->commit();
            throw new Exception("Too many failed OTP attempts. Account locked for 30 minutes.");
        }
        
        // Log failed attempt
        $stmt = $conn->prepare("
            INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
            VALUES (?, 'otp', 'otp_failed', ?, ?, ?)
        ");
        $stmt->execute([
            $userId,
            json_encode(['reason' => 'invalid_code', 'attempts' => $newAttempts]),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        $conn->commit();
        throw new Exception("Invalid OTP code. Please check your email and try again.");
    }
    
    // OTP verification successful
    
    // Mark OTP as used
    $stmt = $conn->prepare("UPDATE user_otp SET used = 1 WHERE id = ?");
    $stmt->execute([$otpRecord['id']]);
    
    // Clear failed attempts
    $stmt = $conn->prepare("DELETE FROM user_login_attempts WHERE user_id = ? AND attempt_type = 'otp'");
    $stmt->execute([$userId]);
    
    // Update last login
    $stmt = $conn->prepare("UPDATE users SET last_active = NOW() WHERE user_id = ?");
    $stmt->execute([$userId]);
    
    // Log successful OTP verification
    $stmt = $conn->prepare("
        INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
        VALUES (?, 'otp', 'otp_success', ?, ?, ?)
    ");
    $stmt->execute([
        $userId,
        json_encode(['otp_id' => $otpRecord['id']]),
        $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    $conn->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'OTP verification successful',
        'userId' => $userId,
        'username' => $user['username']
    ]);
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error occurred'
    ]);
}
?>
