<?php
require_once 'backend/includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    echo "user_otp table structure:\n";
    $stmt = $conn->prepare('DESCRIBE user_otp');
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $col) {
        echo "- {$col['Field']} ({$col['Type']})\n";
    }
    
    echo "\nSample data:\n";
    $stmt = $conn->prepare('SELECT * FROM user_otp ORDER BY created_at DESC LIMIT 3');
    $stmt->execute();
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($rows)) {
        echo "No OTP records found\n";
    } else {
        foreach ($rows as $row) {
            echo "ID: {$row['id']}, User: {$row['user_id']}, OTP: {$row['otp']}, Expiry: {$row['expiry']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
