/* Withdrawal Page Styles */
.withdrawal-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.withdrawal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f3f4f6;
}

.withdrawal-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.balance-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.balance-label {
  font-size: 0.875rem;
  font-weight: 500;
  opacity: 0.9;
  margin-bottom: 4px;
}

.balance-amount {
  font-size: 1.5rem;
  font-weight: 700;
}

/* Loading and Messages */
.loading {
  text-align: center;
  padding: 40px;
  font-size: 1.125rem;
  color: #6b7280;
}

.error-message {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  color: #dc2626;
  padding: 16px 20px;
  border-radius: 12px;
  margin-bottom: 24px;
  border: 1px solid #fecaca;
  font-weight: 500;
}

.success-message {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  color: #059669;
  padding: 16px 20px;
  border-radius: 12px;
  margin-bottom: 24px;
  border: 1px solid #bbf7d0;
  font-weight: 500;
}

/* No Payment Methods */
.no-payment-methods {
  text-align: center;
  padding: 48px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  border: 2px dashed #e5e7eb;
}

.no-payment-methods h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #374151;
  margin: 0 0 16px 0;
}

.no-payment-methods p {
  font-size: 1rem;
  color: #6b7280;
  margin: 0 0 24px 0;
  line-height: 1.6;
}

/* Form Styles */
.withdrawal-form {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.form-section {
  background: #f8fafc;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.form-section h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #374151;
  margin: 0 0 20px 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #ffffff;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-help {
  display: block;
  font-size: 0.8rem;
  color: #6b7280;
  margin-top: 4px;
  font-style: italic;
}

/* Calculation Section */
.calculation-section {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #bae6fd;
}

.calculation-section h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #0c4a6e;
  margin: 0 0 20px 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.calculation-grid {
  display: grid;
  gap: 12px;
}

.calc-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e0f2fe;
}

.calc-item.highlight {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  font-weight: 700;
  border-color: #2563eb;
}

.calc-label {
  font-weight: 500;
  color: inherit;
}

.calc-value {
  font-weight: 700;
  color: inherit;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.btn-primary,
.btn-secondary {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  min-width: 140px;
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-primary:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 2px solid #e5e7eb;
}

.btn-secondary:hover {
  background: #e5e7eb;
  border-color: #d1d5db;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .withdrawal-container {
    margin: 16px;
    padding: 16px;
  }
  
  .withdrawal-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .balance-display {
    align-items: center;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn-primary,
  .btn-secondary {
    width: 100%;
  }
  
  .calculation-grid {
    gap: 8px;
  }
  
  .calc-item {
    padding: 8px 12px;
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .withdrawal-header h1 {
    font-size: 1.5rem;
  }
  
  .balance-amount {
    font-size: 1.25rem;
  }
  
  .form-section,
  .calculation-section {
    padding: 16px;
  }
}
