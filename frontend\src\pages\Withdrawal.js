import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from '../utils/axiosConfig';
import './Withdrawal.css';

const Withdrawal = () => {
  const navigate = useNavigate();
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [currencies, setCurrencies] = useState([]);
  const [exchangeRates, setExchangeRates] = useState({});
  const [userBalance, setUserBalance] = useState(0);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [withdrawalForm, setWithdrawalForm] = useState({
    payment_method_id: '',
    amount_fancoins: '',
    target_currency_id: ''
  });

  const [calculatedAmounts, setCalculatedAmounts] = useState({
    amount_currency: 0,
    withdrawal_fee: 0,
    net_amount: 0
  });

  useEffect(() => {
    fetchInitialData();
  }, []);

  useEffect(() => {
    calculateAmounts();
  }, [withdrawalForm.amount_fancoins, withdrawalForm.target_currency_id, exchangeRates]);

  const fetchInitialData = async () => {
    try {
      setLoading(true);
      
      // Fetch user payment methods
      const paymentMethodsResponse = await axios.get('/handlers/user_payment_methods.php');
      if (paymentMethodsResponse.data.success) {
        setPaymentMethods(paymentMethodsResponse.data.data.filter(method => 
          method.is_verified && method.status === 'active'
        ));
      }

      // Fetch currencies
      const currenciesResponse = await axios.get('/handlers/currencies.php');
      if (currenciesResponse.data.success) {
        setCurrencies(currenciesResponse.data.data);
      }

      // Fetch exchange rates
      const ratesResponse = await axios.get('/handlers/exchange_rates.php');
      if (ratesResponse.data.success) {
        const ratesMap = {};
        ratesResponse.data.data.forEach(rate => {
          ratesMap[rate.currency_id] = rate.rate_to_fancoin;
        });
        setExchangeRates(ratesMap);
      }

      // Fetch user balance
      const userResponse = await axios.get('/handlers/user_data.php');
      if (userResponse.data.success) {
        setUserBalance(userResponse.data.data.balance || 0);
      }

    } catch (error) {
      console.error('Error fetching data:', error);
      setError('Failed to load withdrawal data. Please refresh the page.');
    } finally {
      setLoading(false);
    }
  };

  const calculateAmounts = () => {
    if (!withdrawalForm.amount_fancoins || !withdrawalForm.target_currency_id || !exchangeRates[withdrawalForm.target_currency_id]) {
      setCalculatedAmounts({ amount_currency: 0, withdrawal_fee: 0, net_amount: 0 });
      return;
    }

    const fancoins = parseFloat(withdrawalForm.amount_fancoins);
    const exchangeRate = exchangeRates[withdrawalForm.target_currency_id];
    
    const amount_currency = fancoins / exchangeRate;
    const withdrawal_fee = fancoins * 0.02; // 2% fee
    const fee_in_currency = withdrawal_fee / exchangeRate;
    const net_amount = amount_currency - fee_in_currency;

    setCalculatedAmounts({
      amount_currency: amount_currency.toFixed(4),
      withdrawal_fee: withdrawal_fee.toFixed(4),
      net_amount: Math.max(0, net_amount).toFixed(4)
    });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setWithdrawalForm(prev => ({
      ...prev,
      [name]: value
    }));
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!withdrawalForm.payment_method_id || !withdrawalForm.amount_fancoins || !withdrawalForm.target_currency_id) {
      setError('Please fill in all required fields');
      return;
    }

    const fancoins = parseFloat(withdrawalForm.amount_fancoins);
    
    if (fancoins <= 0) {
      setError('Amount must be greater than 0');
      return;
    }

    if (fancoins > userBalance) {
      setError('Insufficient balance');
      return;
    }

    if (fancoins < 10) {
      setError('Minimum withdrawal amount is 10 FanCoins');
      return;
    }

    try {
      setSubmitting(true);
      setError('');

      const response = await axios.post('/handlers/withdrawal_requests.php', {
        payment_method_id: parseInt(withdrawalForm.payment_method_id),
        amount_fancoins: fancoins,
        target_currency_id: parseInt(withdrawalForm.target_currency_id)
      });

      if (response.data.success) {
        setSuccess(`Withdrawal request submitted successfully! Reference: ${response.data.reference_number}`);
        setWithdrawalForm({
          payment_method_id: '',
          amount_fancoins: '',
          target_currency_id: ''
        });
        
        // Refresh user balance
        fetchInitialData();
        
        // Redirect to withdrawal history after 3 seconds
        setTimeout(() => {
          navigate('/user/withdrawal-history');
        }, 3000);
      } else {
        setError(response.data.message || 'Failed to submit withdrawal request');
      }
    } catch (error) {
      console.error('Error submitting withdrawal:', error);
      setError('Failed to submit withdrawal request. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const getSelectedCurrency = () => {
    return currencies.find(c => c.currency_id == withdrawalForm.target_currency_id);
  };

  const getSelectedPaymentMethod = () => {
    return paymentMethods.find(pm => pm.payment_method_id == withdrawalForm.payment_method_id);
  };

  if (loading) {
    return (
      <div className="withdrawal-container">
        <div className="loading">Loading withdrawal options...</div>
      </div>
    );
  }

  return (
    <div className="withdrawal-container">
      <div className="withdrawal-header">
        <h1>Withdraw FanCoins</h1>
        <div className="balance-display">
          <span className="balance-label">Available Balance:</span>
          <span className="balance-amount">{userBalance.toFixed(2)} FC</span>
        </div>
      </div>

      {error && <div className="error-message">{error}</div>}
      {success && <div className="success-message">{success}</div>}

      {paymentMethods.length === 0 ? (
        <div className="no-payment-methods">
          <h3>No Payment Methods Available</h3>
          <p>You need to add and verify a payment method before you can withdraw funds.</p>
          <button 
            className="btn-primary"
            onClick={() => navigate('/user/payment-methods')}
          >
            Add Payment Method
          </button>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="withdrawal-form">
          <div className="form-section">
            <h3>Withdrawal Details</h3>
            
            <div className="form-group">
              <label htmlFor="payment_method_id">Payment Method *</label>
              <select
                id="payment_method_id"
                name="payment_method_id"
                value={withdrawalForm.payment_method_id}
                onChange={handleInputChange}
                required
              >
                <option value="">Select payment method</option>
                {paymentMethods.map(method => (
                  <option key={method.payment_method_id} value={method.payment_method_id}>
                    {method.method_name} ({method.method_type.replace('_', ' ')})
                    {method.is_primary && ' - Primary'}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="target_currency_id">Target Currency *</label>
              <select
                id="target_currency_id"
                name="target_currency_id"
                value={withdrawalForm.target_currency_id}
                onChange={handleInputChange}
                required
              >
                <option value="">Select currency</option>
                {currencies.map(currency => (
                  <option key={currency.currency_id} value={currency.currency_id}>
                    {currency.currency_code} ({currency.currency_symbol}) - {currency.currency_name}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="amount_fancoins">Amount (FanCoins) *</label>
              <input
                type="number"
                id="amount_fancoins"
                name="amount_fancoins"
                value={withdrawalForm.amount_fancoins}
                onChange={handleInputChange}
                min="10"
                max={userBalance}
                step="0.01"
                placeholder="Enter amount in FanCoins"
                required
              />
              <small className="form-help">Minimum: 10 FC, Maximum: {userBalance.toFixed(2)} FC</small>
            </div>
          </div>

          {withdrawalForm.amount_fancoins && withdrawalForm.target_currency_id && (
            <div className="calculation-section">
              <h3>Withdrawal Summary</h3>
              <div className="calculation-grid">
                <div className="calc-item">
                  <span className="calc-label">Amount (FanCoins):</span>
                  <span className="calc-value">{parseFloat(withdrawalForm.amount_fancoins).toFixed(2)} FC</span>
                </div>
                <div className="calc-item">
                  <span className="calc-label">Exchange Rate:</span>
                  <span className="calc-value">
                    1 FC = {exchangeRates[withdrawalForm.target_currency_id] ? 
                      (1 / exchangeRates[withdrawalForm.target_currency_id]).toFixed(6) : '0'} {getSelectedCurrency()?.currency_code}
                  </span>
                </div>
                <div className="calc-item">
                  <span className="calc-label">Gross Amount:</span>
                  <span className="calc-value">
                    {getSelectedCurrency()?.currency_symbol}{calculatedAmounts.amount_currency} {getSelectedCurrency()?.currency_code}
                  </span>
                </div>
                <div className="calc-item">
                  <span className="calc-label">Withdrawal Fee (2%):</span>
                  <span className="calc-value">{calculatedAmounts.withdrawal_fee} FC</span>
                </div>
                <div className="calc-item highlight">
                  <span className="calc-label">Net Amount:</span>
                  <span className="calc-value">
                    {getSelectedCurrency()?.currency_symbol}{calculatedAmounts.net_amount} {getSelectedCurrency()?.currency_code}
                  </span>
                </div>
              </div>
            </div>
          )}

          <div className="form-actions">
            <button 
              type="button" 
              className="btn-secondary"
              onClick={() => navigate('/user/withdrawal-history')}
            >
              View History
            </button>
            <button 
              type="submit" 
              className="btn-primary"
              disabled={submitting || !withdrawalForm.payment_method_id || !withdrawalForm.amount_fancoins || !withdrawalForm.target_currency_id}
            >
              {submitting ? 'Processing...' : 'Submit Withdrawal Request'}
            </button>
          </div>
        </form>
      )}
    </div>
  );
};

export default Withdrawal;
