/**
 * Team Logo Utilities
 * Centralized system for handling team logo URLs consistently across the application
 */
import React from 'react';

// Get the correct base URL for team logos
const getLogoBaseUrl = () => {
    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === 'development' ||
                         window.location.hostname === 'localhost' ||
                         window.location.hostname === '127.0.0.1';

    if (isDevelopment) {
        // In development, use the proxy path
        return '/FanBet247/backend';
    } else {
        // In production, construct the full URL
        const projectPath = window.location.pathname.split('/')[1] || 'FanBet247';
        return `${window.location.origin}/${projectPath}/backend`;
    }
};

/**
 * Get the complete URL for a team logo
 * @param {string|Object} teamData - Either team name string or team object with logo property
 * @param {Array} teams - Array of team objects (optional, used when teamData is a string)
 * @returns {string} Complete logo URL or default logo URL
 */
export const getTeamLogoUrl = (teamData, teams = []) => {
    let logoPath = null;

    // Handle different input types
    if (typeof teamData === 'string') {
        // teamData is a team name, find the team in the teams array
        const team = teams.find(t => t.name === teamData);
        logoPath = team?.logo;
    } else if (teamData && typeof teamData === 'object') {
        // teamData is a team object
        logoPath = teamData.logo;
    }

    // If no logo path found, return default
    if (!logoPath) {
        return '/images/default-team-logo.png';
    }

    // Construct the complete URL
    const baseUrl = getLogoBaseUrl();
    
    // Handle different logo path formats
    if (logoPath.startsWith('http')) {
        // Already a complete URL
        return logoPath;
    } else if (logoPath.startsWith('/')) {
        // Absolute path from root
        return `${baseUrl}${logoPath}`;
    } else {
        // Relative path (most common case: "uploads/filename.png")
        return `${baseUrl}/${logoPath}`;
    }
};

/**
 * Get team logo URL by team name
 * @param {string} teamName - Name of the team
 * @param {Array} teams - Array of team objects
 * @returns {string} Complete logo URL or default logo URL
 */
export const getTeamLogoByName = (teamName, teams) => {
    return getTeamLogoUrl(teamName, teams);
};

/**
 * Get team logo URL from team object
 * @param {Object} team - Team object with logo property
 * @returns {string} Complete logo URL or default logo URL
 */
export const getTeamLogoFromObject = (team) => {
    return getTeamLogoUrl(team);
};

/**
 * Create a team logo component with error handling
 * @param {string} teamName - Name of the team
 * @param {Array} teams - Array of team objects
 * @param {Object} props - Additional props for the img element
 * @returns {JSX.Element} Image element with error handling
 */
export const createTeamLogoElement = (teamName, teams, props = {}) => {
    const logoUrl = getTeamLogoByName(teamName, teams);
    
    return React.createElement('img', {
        src: logoUrl,
        alt: teamName || 'Team Logo',
        onError: (e) => {
            e.target.src = '/images/default-team-logo.png';
        },
        ...props
    });
};

/**
 * Hook for team logo functionality
 * @param {Array} teams - Array of team objects
 * @returns {Object} Object with logo utility functions
 */
export const useTeamLogos = (teams) => {
    const getLogoUrl = (teamName) => getTeamLogoByName(teamName, teams);
    
    const getLogoFromObject = (team) => getTeamLogoFromObject(team);
    
    const createLogoElement = (teamName, props = {}) => 
        createTeamLogoElement(teamName, teams, props);

    return {
        getLogoUrl,
        getLogoFromObject,
        createLogoElement,
        teams
    };
};

// Default export for backward compatibility
export default {
    getTeamLogoUrl,
    getTeamLogoByName,
    getTeamLogoFromObject,
    createTeamLogoElement,
    useTeamLogos
};
