<?php
echo "🔧 Testing Teams API Endpoint\n";
echo "============================\n\n";

// Test the API endpoint directly
$url = 'http://localhost:3001/backend/handlers/team_management.php';

echo "Testing URL: $url\n\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, false);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $httpCode\n";

if ($error) {
    echo "❌ cURL Error: $error\n";
} else {
    echo "✅ Response received\n";
    echo "Response length: " . strlen($response) . " bytes\n";
    echo "Response content:\n";
    echo "================\n";
    echo $response;
    echo "\n================\n";
    
    // Try to decode JSON
    $decoded = json_decode($response, true);
    if ($decoded) {
        echo "✅ Valid JSON response\n";
        echo "Status: " . ($decoded['status'] ?? 'unknown') . "\n";
        echo "Message: " . ($decoded['message'] ?? 'no message') . "\n";
        if (isset($decoded['data'])) {
            echo "Data count: " . count($decoded['data']) . " items\n";
        }
    } else {
        echo "❌ Invalid JSON response\n";
    }
}
?>
