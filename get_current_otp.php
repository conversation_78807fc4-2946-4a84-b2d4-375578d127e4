<?php
require_once 'backend/includes/db_connect.php';

try {
    $conn = getDBConnection();
    $stmt = $conn->prepare('SELECT otp FROM user_otp WHERE user_id = 4 AND used = 0 ORDER BY created_at DESC LIMIT 1');
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo $result['otp'];
    } else {
        echo 'No valid OTP found';
    }
} catch (Exception $e) {
    echo 'Error: ' . $e->getMessage();
}
?>
