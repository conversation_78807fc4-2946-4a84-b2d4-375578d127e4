import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { API_BASE_URL } from '../config';
import welcomeService from '../services/welcomeService';
import LiveMatchCard from '../components/WelcomePage/LiveMatchCard';
import ChallengeCard from '../components/WelcomePage/ChallengeCard';
import './NewWelcomePage.css';

const NewWelcomePage = () => {
    const navigate = useNavigate();
    const [recentBets, setRecentBets] = useState([]);
    const [liveMatches, setLiveMatches] = useState([]);
    const [recentChallenges, setRecentChallenges] = useState([]);
    const [topLeagues, setTopLeagues] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [currentSlide, setCurrentSlide] = useState(0);
    const [userBalance, setUserBalance] = useState(null);

    // Check if user is logged in
    const isLoggedIn = localStorage.getItem('userId') && localStorage.getItem('userToken');
    const username = localStorage.getItem('username');

    useEffect(() => {
        fetchWelcomeData();
        
        // Auto-slide for hero section
        const slideInterval = setInterval(() => {
            setCurrentSlide(prev => (prev + 1) % 3);
        }, 5000);

        // Simulate live odds updates
        const oddsInterval = setInterval(() => {
            updateLiveOdds();
        }, 3000);

        return () => {
            clearInterval(slideInterval);
            clearInterval(oddsInterval);
        };
    }, []);

    const fetchWelcomeData = async () => {
        try {
            setLoading(true);
            setError('');

            const userId = isLoggedIn ? localStorage.getItem('userId') : null;

            // Use the welcome service for better data management
            const welcomeData = await welcomeService.getWelcomePageData({
                betsLimit: 6,
                matchesLimit: 6,
                challengesLimit: 4,
                leaguesLimit: 7,
                userId: userId
            });

            if (welcomeData.success) {
                const { data } = welcomeData;

                // Set data from service responses
                if (data.recentBets.success) {
                    setRecentBets(data.recentBets.bets || []);
                }

                if (data.liveMatches.success) {
                    setLiveMatches(data.liveMatches.matches || []);
                }

                if (data.recentChallenges.success) {
                    setRecentChallenges(data.recentChallenges.challenges || []);
                }

                if (data.topLeagues.success) {
                    setTopLeagues(data.topLeagues.leagues || []);
                }

                if (data.userBalance.success && isLoggedIn) {
                    setUserBalance(data.userBalance.balance || 0);
                }
            } else {
                setError(welcomeData.error || 'Failed to load welcome page data');
            }

        } catch (err) {
            console.error('Error fetching welcome data:', err);
            setError('Failed to load data. Please try again later.');
        } finally {
            setLoading(false);
        }
    };

    const updateLiveOdds = () => {
        setLiveMatches(prev => {
            if (prev.length === 0) return prev;
            return welcomeService.updateLiveOdds(prev);
        });
    };

    const handleBetClick = (betData) => {
        if (!isLoggedIn) {
            // Redirect to login if not authenticated
            sessionStorage.setItem('redirectAfterLogin', window.location.pathname);
            navigate('/login');
            return;
        }

        // Navigate to betting page with bet data
        navigate(`/user/join-challenge/${betData.matchId}`, {
            state: { betData }
        });
    };

    const getTeamLogo = (teamName) => {
        if (!teamName) return '/images/default-team.png';
        return `${API_BASE_URL}/uploads/teams/${teamName.toLowerCase().replace(/\s+/g, '_')}.png`;
    };

    const getLeagueIcon = (leagueName) => {
        const leagueIcons = {
            'Premier League': 'https://media.api-sports.io/football/leagues/39.png',
            'La Liga': 'https://media.api-sports.io/football/leagues/140.png',
            'Bundesliga': 'https://media.api-sports.io/football/leagues/78.png',
            'Serie A': 'https://media.api-sports.io/football/leagues/135.png',
            'Ligue 1': 'https://media.api-sports.io/football/leagues/61.png',
            'Champions League': 'https://media.api-sports.io/football/leagues/1.png',
            'Brasileirão': 'https://media.api-sports.io/football/leagues/71.png'
        };
        return leagueIcons[leagueName] || '/images/default-league.png';
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };

    const handleLogin = () => {
        navigate('/login');
    };

    const handleRegister = () => {
        navigate('/register');
    };

    const handleDashboard = () => {
        navigate('/user/dashboard');
    };

    const slides = [
        {
            title: "Champions League Final Boost",
            subtitle: "Get enhanced odds 50% on all bets for today's big match!",
            buttonText: "Claim Offer",
            background: "linear-gradient(135deg, #0B5A27 0%, #1E8449 100%)",
            image: "https://cdn.pixabay.com/photo/2015/05/26/23/52/champions-league-785983_1280.png"
        },
        {
            title: "Welcome Bonus",
            subtitle: "Join FanBet247 and get 100% bonus on your first deposit!",
            buttonText: "Sign Up Now",
            background: "linear-gradient(135deg, #145A32 0%, #0B5A27 100%)",
            image: "https://cdn.pixabay.com/photo/2016/08/08/11/38/money-1578377_1280.png"
        },
        {
            title: "Live Betting",
            subtitle: "Experience the thrill of live betting with real-time odds!",
            buttonText: "Bet Live",
            background: "linear-gradient(135deg, #1E8449 0%, #145A32 100%)",
            image: "https://cdn.pixabay.com/photo/2017/02/21/01/49/premier-league-2084730_1280.png"
        }
    ];

    if (loading) {
        return (
            <div className="welcome-loading">
                <div className="loading-spinner"></div>
                <p>Loading FanBet247...</p>
            </div>
        );
    }

    return (
        <div className="welcome-page">
            {/* Header */}
            <header className="welcome-header">
                <div className="header-container">
                    <div className="header-left">
                        <div className="logo">
                            <div className="logo-icon">
                                <i className="fas fa-futbol"></i>
                            </div>
                            <span className="logo-text">FanBet247</span>
                        </div>
                        
                        <nav className="main-nav">
                            <Link to="/" className="nav-link active">Home</Link>
                            <Link to="/user/leagues" className="nav-link">Leagues</Link>
                            <Link to="/user/challenges" className="nav-link">Challenges</Link>
                            <a href="#live-section" className="nav-link">Live</a>
                            <a href="#about" className="nav-link">About</a>
                        </nav>
                    </div>
                    
                    <div className="header-right">
                        {isLoggedIn ? (
                            <>
                                <div className="user-balance">
                                    <i className="fas fa-coins"></i>
                                    <span>{formatCurrency(userBalance || 0)}</span>
                                </div>
                                <div className="user-menu">
                                    <span className="username">Welcome, {username}</span>
                                    <button onClick={handleDashboard} className="dashboard-btn">
                                        Dashboard
                                    </button>
                                </div>
                            </>
                        ) : (
                            <div className="auth-buttons">
                                <button onClick={handleLogin} className="login-btn">Login</button>
                                <button onClick={handleRegister} className="register-btn">Sign Up</button>
                            </div>
                        )}
                        
                        <button className="mobile-menu-btn">
                            <i className="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </header>

            <div className="welcome-layout">
                {/* Sidebar */}
                <aside className="welcome-sidebar">
                    <div className="sidebar-header">
                        <h2>Top Leagues</h2>
                        <span className="live-indicator">
                            <span className="pulse-dot"></span>
                            LIVE
                        </span>
                    </div>
                    
                    <div className="leagues-list">
                        {topLeagues.length > 0 ? (
                            topLeagues.map((league, index) => (
                                <div key={league.league_id || index} className="league-item">
                                    <div className="league-icon">
                                        <img
                                            src={league.icon_url || getLeagueIcon(league.name)}
                                            alt={league.name}
                                            onError={(e) => {
                                                e.target.src = '/images/default-league.png';
                                            }}
                                        />
                                    </div>
                                    <div className="league-info">
                                        <span className="league-name">{league.name}</span>
                                        <span className="match-count">
                                            {league.member_count || 0} members
                                            {league.live_status === 'live' && (
                                                <span className="live-indicator-small">
                                                    <span className="pulse-dot-small"></span>
                                                    LIVE
                                                </span>
                                            )}
                                        </span>
                                    </div>
                                    <div className="league-badge">
                                        {league.active_challenges || league.recent_bets || Math.floor(Math.random() * 20) + 5}
                                    </div>
                                </div>
                            ))
                        ) : (
                            <div className="no-leagues">
                                <p>Loading leagues...</p>
                            </div>
                        )}
                    </div>
                    
                    <div className="quick-bet-section">
                        <h3>Quick Bet</h3>
                        <input 
                            type="number" 
                            placeholder="Enter amount" 
                            className="quick-bet-input"
                        />
                        <button className="quick-bet-btn">Place Bet</button>
                    </div>
                </aside>

                {/* Main Content */}
                <main className="welcome-main">
                    {/* Hero Section */}
                    <section className="hero-section">
                        <div className="hero-slider">
                            {slides.map((slide, index) => (
                                <div 
                                    key={index}
                                    className={`slide ${index === currentSlide ? 'active' : ''}`}
                                    style={{ background: slide.background }}
                                >
                                    <div className="slide-content">
                                        <div className="slide-text">
                                            <h1>{slide.title}</h1>
                                            <p>{slide.subtitle}</p>
                                            <button className="cta-button">
                                                {slide.buttonText}
                                            </button>
                                        </div>
                                        <div className="slide-image">
                                            <img src={slide.image} alt={slide.title} />
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                        
                        <div className="slider-controls">
                            {slides.map((_, index) => (
                                <button
                                    key={index}
                                    className={`control-dot ${index === currentSlide ? 'active' : ''}`}
                                    onClick={() => setCurrentSlide(index)}
                                />
                            ))}
                        </div>
                    </section>

                    {/* Live Challenges Section */}
                    <section id="live-section" className="live-challenges-section">
                        <div className="section-header">
                            <h2>Live Challenges</h2>
                            <div className="live-badge">
                                <span className="pulse-dot"></span>
                                LIVE
                            </div>
                            <Link to="/user/challenges" className="view-all-link">View All</Link>
                        </div>

                        <div className="live-challenges-grid">
                            {liveMatches.length > 0 ? (
                                liveMatches.slice(0, 6).map((challenge, index) => (
                                    <div key={challenge.challenge_id || index} className="live-challenge-card">
                                        <div className="challenge-header">
                                            <div className="challenge-info">
                                                <div className="challenge-teams">
                                                    <img
                                                        src={challenge.team_a_logo || getTeamLogo(challenge.team_a)}
                                                        alt={challenge.team_a}
                                                        className="challenge-team-logo"
                                                        onError={(e) => {
                                                            e.target.src = '/images/default-team.png';
                                                        }}
                                                    />
                                                    <span className="challenge-vs">VS</span>
                                                    <img
                                                        src={challenge.team_b_logo || getTeamLogo(challenge.team_b)}
                                                        alt={challenge.team_b}
                                                        className="challenge-team-logo"
                                                        onError={(e) => {
                                                            e.target.src = '/images/default-team.png';
                                                        }}
                                                    />
                                                </div>
                                                <h3>{challenge.team_a || 'Team A'} vs {challenge.team_b || 'Team B'}</h3>
                                                <span className="challenge-type">
                                                    {challenge.league_name || 'Prediction Challenge'}
                                                </span>
                                            </div>
                                            <div className={`live-indicator ${challenge.urgency_status || 'active'}`}>
                                                <span className="pulse-dot"></span>
                                                {challenge.urgency_status === 'urgent' ? 'URGENT' : 'LIVE'}
                                            </div>
                                        </div>
                                        <div className="challenge-details">
                                            <div className="detail">
                                                <span className="label">Prize Pool</span>
                                                <span className="value">
                                                    {formatCurrency(challenge.total_prize_pool || 100)}
                                                </span>
                                            </div>
                                            <div className="detail">
                                                <span className="label">Participants</span>
                                                <span className="value">{challenge.total_bets || 0}</span>
                                            </div>
                                            <div className="detail">
                                                <span className="label">Time Left</span>
                                                <span className="value">
                                                    {challenge.time_display || '2h 15m'}
                                                </span>
                                            </div>
                                        </div>
                                        <button
                                            className="join-challenge-btn"
                                            onClick={() => navigate(`/user/join-challenge/${challenge.challenge_id}`)}
                                        >
                                            Join Challenge
                                        </button>
                                    </div>
                                ))
                            ) : (
                                <div className="no-challenges">
                                    <div className="no-challenges-icon">🎯</div>
                                    <h3>No Live Challenges</h3>
                                    <p>Check back soon for live challenges!</p>
                                </div>
                            )}
                        </div>
                    </section>

                    {/* Recent Bets Section */}
                    <section className="recent-bets-section">
                        <div className="section-header">
                            <h2>Recent Bets</h2>
                            <Link to="/user/recent-bets" className="view-all-link">View All</Link>
                        </div>

                        <div className="recent-bets-table">
                            <div className="table-header">
                                <span>Match</span>
                                <span>Selection</span>
                                <span>Odds</span>
                                <span>Stake</span>
                                <span>Potential Win</span>
                                <span>Status</span>
                            </div>

                            <div className="table-body">
                                {recentBets.length > 0 ? (
                                    recentBets.slice(0, 5).map((bet, index) => (
                                        <div key={bet.bet_id || index} className="table-row">
                                            <div className="match-info">
                                                <div className="teams">
                                                    <img
                                                        src={bet.team_a_logo || getTeamLogo(bet.team_a)}
                                                        alt={bet.team_a}
                                                        className="team-logo-small"
                                                        onError={(e) => {
                                                            e.target.src = '/images/default-team.png';
                                                        }}
                                                    />
                                                    <div className="team-names">
                                                        <span className="match-teams">
                                                            {bet.team_a} vs {bet.team_b}
                                                        </span>
                                                        {bet.league_name && (
                                                            <span className="league-name">{bet.league_name}</span>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className="match-date">{formatDate(bet.created_at)}</div>
                                            </div>
                                            <div className="selection">
                                                {bet.bet_choice_user1 || 'Team A Win'}
                                            </div>
                                            <div className="odds">
                                                {bet.odds_user1 || '2.10'}
                                            </div>
                                            <div className="stake">
                                                {formatCurrency(bet.amount_user1 || 50)}
                                            </div>
                                            <div className="potential-win">
                                                {formatCurrency(bet.potential_return_user1 || 105)}
                                            </div>
                                            <div className="status">
                                                <span className={`status-badge ${bet.display_status || bet.bet_status || 'pending'}`}>
                                                    {bet.display_status === 'won' ? 'Won' :
                                                     bet.display_status === 'lost' ? 'Lost' :
                                                     bet.display_status === 'draw' ? 'Draw' :
                                                     bet.display_status === 'active' ? 'Active' : 'Pending'}
                                                </span>
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <div className="no-bets-message">
                                        <div className="no-bets-icon">📊</div>
                                        <h3>No Recent Bets</h3>
                                        <p>Your recent betting activity will appear here.</p>
                                        <button
                                            className="start-betting-btn"
                                            onClick={() => navigate('/user/challenges')}
                                        >
                                            Start Betting
                                        </button>
                                    </div>
                                )}
                            </div>
                        </div>
                    </section>

                    {/* Recent Challenges Section */}
                    <section className="recent-challenges-section">
                        <div className="section-header">
                            <h2>Recent Challenges</h2>
                            <Link to="/user/challenges" className="view-all-link">View All</Link>
                        </div>

                        <div className="challenges-grid">
                            {Array.from({ length: 4 }, (_, index) => (
                                <ChallengeCard
                                    key={index}
                                    challenge={recentChallenges[index] || {}}
                                    index={index}
                                />
                            ))}
                        </div>
                    </section>
                </main>
            </div>

            {/* Footer */}
            <footer className="welcome-footer">
                <div className="footer-container">
                    <div className="footer-grid">
                        <div className="footer-section">
                            <h3>FanBet247</h3>
                            <p>The ultimate soccer betting experience with live odds, expert predictions, and exclusive promotions.</p>
                            <div className="social-links">
                                <a href="#" className="social-link">
                                    <i className="fab fa-facebook-f"></i>
                                </a>
                                <a href="#" className="social-link">
                                    <i className="fab fa-twitter"></i>
                                </a>
                                <a href="#" className="social-link">
                                    <i className="fab fa-instagram"></i>
                                </a>
                                <a href="#" className="social-link">
                                    <i className="fab fa-telegram"></i>
                                </a>
                            </div>
                        </div>

                        <div className="footer-section">
                            <h4>Quick Links</h4>
                            <ul>
                                <li><Link to="/">Home</Link></li>
                                <li><Link to="/user/leagues">Leagues</Link></li>
                                <li><Link to="/user/challenges">Live Betting</Link></li>
                                <li><a href="#about">About</a></li>
                            </ul>
                        </div>

                        <div className="footer-section">
                            <h4>Help & Support</h4>
                            <ul>
                                <li><a href="#faq">FAQ</a></li>
                                <li><a href="#how-to-bet">How to Bet</a></li>
                                <li><a href="#payment">Payment Methods</a></li>
                                <li><a href="#contact">Contact Us</a></li>
                            </ul>
                        </div>

                        <div className="footer-section">
                            <h4>Legal</h4>
                            <ul>
                                <li><a href="#terms">Terms & Conditions</a></li>
                                <li><a href="#privacy">Privacy Policy</a></li>
                                <li><a href="#responsible">Responsible Gambling</a></li>
                                <li><a href="#licenses">Licenses</a></li>
                            </ul>
                        </div>
                    </div>

                    <div className="footer-bottom">
                        <div className="copyright">
                            © 2025 FanBet247. All rights reserved.
                        </div>
                        <div className="footer-badges">
                            <span className="badge">18+</span>
                            <span className="badge">Responsible Gaming</span>
                            <span className="badge">Secure</span>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    );
};

export default NewWelcomePage;
