@echo off
echo 🧹 Clearing React Build Cache...
echo ================================

echo 📁 Removing node_modules/.cache...
if exist "node_modules\.cache" (
    rmdir /s /q "node_modules\.cache"
    echo ✅ Cache directory removed
) else (
    echo ℹ️ Cache directory not found
)

echo 📁 Removing build directory...
if exist "build" (
    rmdir /s /q "build"
    echo ✅ Build directory removed
) else (
    echo ℹ️ Build directory not found
)

echo 🔄 Restarting development server...
echo Please restart your development server with: npm start

echo.
echo 🎉 Cache cleared successfully!
echo The ESLint error should be resolved after restarting the dev server.
pause
