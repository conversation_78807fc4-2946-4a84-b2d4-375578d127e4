<?php
header('Content-Type: text/plain');

echo "🧪 TESTING OTP AUTHENTICATION FLOW\n";
echo "===================================\n\n";

// Test 1: Check if a user has OTP enabled
echo "📋 Test 1: Checking user OTP settings\n";
echo "-------------------------------------\n";

require_once 'backend/includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Get a test user
    $stmt = $conn->prepare("SELECT user_id, username, email, otp_enabled, tfa_enabled, auth_method FROM users WHERE username = 'demohomexx' LIMIT 1");
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "✅ Found test user: {$user['username']}\n";
        echo "   Email: {$user['email']}\n";
        echo "   OTP Enabled: " . ($user['otp_enabled'] ? 'Yes' : 'No') . "\n";
        echo "   2FA Enabled: " . ($user['tfa_enabled'] ? 'Yes' : 'No') . "\n";
        echo "   Auth Method: {$user['auth_method']}\n\n";
        
        // Test 2: Enable OTP for this user if not enabled
        if (!$user['otp_enabled']) {
            echo "📋 Test 2: Enabling OTP for test user\n";
            echo "------------------------------------\n";
            
            $stmt = $conn->prepare("UPDATE users SET otp_enabled = 1, auth_method = 'password_otp' WHERE user_id = ?");
            $stmt->execute([$user['user_id']]);
            
            echo "✅ OTP enabled for user {$user['username']}\n\n";
        } else {
            echo "✅ OTP already enabled for user {$user['username']}\n\n";
        }
        
        // Test 3: Check if user_otp table exists and is accessible
        echo "📋 Test 3: Checking user_otp table\n";
        echo "-----------------------------------\n";
        
        $stmt = $conn->prepare("DESCRIBE user_otp");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "✅ user_otp table structure:\n";
        foreach ($columns as $column) {
            echo "   - {$column['Field']} ({$column['Type']})\n";
        }
        echo "\n";
        
        // Test 4: Check SMTP settings
        echo "📋 Test 4: Checking SMTP configuration\n";
        echo "--------------------------------------\n";
        
        $stmt = $conn->prepare("SELECT * FROM smtp_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1");
        $stmt->execute();
        $smtp = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($smtp) {
            echo "✅ SMTP configured:\n";
            echo "   Host: {$smtp['host']}\n";
            echo "   Port: {$smtp['port']}\n";
            echo "   Username: {$smtp['username']}\n";
            echo "   From Email: {$smtp['from_email']}\n\n";
        } else {
            echo "❌ No active SMTP configuration found\n";
            echo "   OTP emails cannot be sent without SMTP setup\n\n";
        }
        
        echo "🎉 OTP FLOW TEST COMPLETE!\n\n";
        echo "📝 NEXT STEPS:\n";
        echo "1. Try logging in with username: {$user['username']}\n";
        echo "2. Use password: loving12 (test password)\n";
        echo "3. System should now require OTP verification\n";
        echo "4. Check your email for the OTP code\n\n";
        
    } else {
        echo "❌ Test user 'demohomexx' not found\n";
        echo "   Please create a test user first\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
